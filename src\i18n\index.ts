export type Locale = 'en' | 'zh';
import { inject, shallowRef } from 'vue';
import en from './locales/en';
import zh from './locales/zh';

export const messages = { en, zh } as const;
export const I18N_KEY: unique symbol = Symbol('i18n');

export interface I18nContext {
  locale: { value: Locale };
  t: (path: string) => string;
  setLocale: (l: Locale) => void;
}

const LOCALE_STORAGE_KEY = 'app-locale';

export function createI18n(initial: Locale = (localStorage.getItem(LOCALE_STORAGE_KEY) as Locale) || 'zh'): I18nContext {
  const locale = shallowRef<Locale>(initial);
  const t = (path: string) => {
    const cur = locale.value;
    const segs = path.split('.');
    // @ts-expect-error index
    let node: any = messages[cur];
    for (const s of segs) node = node?.[s];
    return (node ?? path) as string;
  };
  const setLocale = (l: Locale) => {
    locale.value = l;
    localStorage.setItem(LOCALE_STORAGE_KEY, l);
    document.documentElement.setAttribute('lang', l);
  };
  setLocale(locale.value);
  return { locale, t, setLocale };
}

export function useI18n(): I18nContext {
  const ctx = inject<I18nContext>(I18N_KEY as any);
  if (!ctx) throw new Error('i18n context not provided');
  return ctx;
}

