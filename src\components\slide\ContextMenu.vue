<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from 'vue'

const props = defineProps<{ x: number, y: number, items: Array<{ key: string, label: string }> }>()
const emit = defineEmits<{ select: [string], close: [] }>()

const menu = ref<HTMLDivElement | null>(null)

function onDocClick(e: MouseEvent) {
  if (!menu.value) return
  if (!menu.value.contains(e.target as Node)) emit('close')
}

onMounted(() => document.addEventListener('mousedown', onDocClick))
onBeforeUnmount(() => document.removeEventListener('mousedown', onDocClick))
</script>

<template>
  <div class="ctx" ref="menu" :style="{ left: props.x + 'px', top: props.y + 'px' }">
    <div v-for="it in props.items" :key="it.key" class="item" @click="emit('select', it.key)">{{ it.label }}</div>
  </div>
</template>

<style scoped>
.ctx { position: fixed; z-index: 1000; min-width: 160px; background: var(--surface); color: var(--fg); box-shadow: 0 6px 24px rgba(0,0,0,.2); border: 1px solid var(--border); border-radius: 8px; padding: 4px; }
.item { padding: 6px 10px; border-radius: 6px; cursor: pointer; }
.item:hover { background: var(--surface-elev); }
</style>

