<script setup lang="ts">
import { themes } from '../../utils/themes'
import { useI18n } from '../../i18n'
import { usePresentationStore } from '../../stores/presentation'

const { t, locale } = useI18n()
const store = usePresentationStore()

function selectTheme(themeId: string) {
  store.setTheme(themeId)
}
</script>

<template>
  <div class="theme-picker">
    <div class="title">{{ t('theme.chooseTheme') }}</div>
    <div class="themes">
      <div 
        v-for="theme in themes" 
        :key="theme.id" 
        class="theme-item"
        :class="{ active: store.doc.theme === theme.id }"
        @click="selectTheme(theme.id)"
      >
        <div class="theme-preview">
          <div class="color-bar">
            <div class="color" :style="{ background: theme.colors.primary }"></div>
            <div class="color" :style="{ background: theme.colors.secondary }"></div>
            <div class="color" :style="{ background: theme.colors.accent }"></div>
          </div>
          <div class="sample-text" :style="{ color: theme.colors.text, background: theme.colors.background }">
            Aa
          </div>
        </div>
        <div class="theme-name">{{ locale.value === 'zh' ? theme.nameZh : theme.name }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.theme-picker {
  padding: 16px;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,.1);
  min-width: 280px;
}

.title {
  font-weight: 600;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border);
}

.themes {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.theme-item {
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.theme-item:hover {
  border-color: var(--border);
}

.theme-item.active {
  border-color: #3b82f6;
  background: var(--surface-elev);
}

.theme-preview {
  margin-bottom: 8px;
}

.color-bar {
  display: flex;
  height: 20px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.color {
  flex: 1;
}

.sample-text {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.theme-name {
  font-size: 12px;
  text-align: center;
  color: var(--muted);
}
</style>
