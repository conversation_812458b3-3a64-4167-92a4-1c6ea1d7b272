import { vi } from 'vitest'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock URL constructor
global.URL = class URL {
  constructor(url: string) {
    this.href = url
    this.searchParams = new URLSearchParams()
  }
  href: string
  searchParams: URLSearchParams
}

// Mock URLSearchParams
global.URLSearchParams = class URLSearchParams {
  private params = new Map<string, string>()
  
  has(key: string) {
    return this.params.has(key)
  }
  
  get(key: string) {
    return this.params.get(key)
  }
  
  set(key: string, value: string) {
    this.params.set(key, value)
  }
}
