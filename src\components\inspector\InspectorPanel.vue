<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'

const store = usePresentationStore()
const { selectedElement } = storeToRefs(store)
const { t } = useI18n()

function number(v: any, d=0){ return isFinite(+v) ? +v : d }
function updateX(e: Event) { store.updateElementFrame(selectedElement.value!.id, { x: number((e.target as HTMLInputElement).value) }) }
function updateY(e: Event) { store.updateElementFrame(selectedElement.value!.id, { y: number((e.target as HTMLInputElement).value) }) }
function updateW(e: Event) { store.updateElementFrame(selectedElement.value!.id, { width: number((e.target as HTMLInputElement).value) }) }
function updateH(e: Event) { store.updateElementFrame(selectedElement.value!.id, { height: number((e.target as HTMLInputElement).value) }) }
function updateR(e: Event) { store.updateElementFrame(selectedElement.value!.id, { rotate: number((e.target as HTMLInputElement).value) }) }
</script>

<template>
  <div class="panel">
    <div class="panel-title">{{ t('inspector.geometry') }}</div>
    <template v-if="selectedElement">
      <div class="row"><label>X</label><input type="number" :value="selectedElement.frame.x" @change="updateX"/></div>
      <div class="row"><label>Y</label><input type="number" :value="selectedElement.frame.y" @change="updateY"/></div>
      <div class="row"><label>W</label><input type="number" :value="selectedElement.frame.width" @change="updateW"/></div>
      <div class="row"><label>H</label><input type="number" :value="selectedElement.frame.height" @change="updateH"/></div>
      <div class="row"><label>°</label><input type="number" :value="selectedElement.frame.rotate || 0" @change="updateR"/></div>
    </template>
    <template v-else>
      <div class="empty">{{ t('inspector.noSelection') }}</div>
    </template>
  </div>
</template>

<style scoped>
.panel { display: flex; flex-direction: column; gap: 8px; padding: 8px; }
.panel-title { font-weight: 600; font-size: 14px; margin-bottom: 8px; color: var(--fg); }
.row { display: grid; grid-template-columns: 24px 1fr; gap: 8px; align-items: center; }
input { background: var(--surface-elev); color: var(--fg); border: 1px solid var(--border); border-radius: 6px; padding: 4px 6px; }
.empty { color: var(--muted); font-size: 12px; }
</style>

