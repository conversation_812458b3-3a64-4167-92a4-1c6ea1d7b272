export default {
  app: {
    title: 'Slides',
    theme: 'Theme',
    language: 'Language',
    light: 'Light',
    dark: 'Dark',
    system: 'System',
    export: 'Export',
    import: 'Import',
    exportJSON: 'Export JSON',
    exportHTML: 'Export HTML',
    importJSON: 'Import JSON',
    presenterView: 'Presenter View',
    grid: 'Grid'
  },
  sidebar: {
    title: 'Slides',
    menu: {
      new: 'New slide',
      duplicate: 'Duplicate',
      delete: 'Delete',
      hide: 'Hide/Unhide',
      rename: 'Rename',
      layout: 'Change layout'
    }
  },
  canvas: {
    placeholder: 'Canvas (Fabric.js to be integrated)',
    canvas: 'Canvas',
    sourceCode: 'Source Code'
  },
  inspector: {
    title: 'Inspector',
    geometry: 'Geometry',
    textStyle: 'Text Style',
    position: 'Position & Transition',
    noSelection: 'No selection'
  },
  textStyle: {
    size: 'Size',
    color: 'Color',
    align: 'Align',
    left: 'Left',
    center: 'Center',
    right: 'Right'
  },
  impress: {
    position2d: '2D Position',
    scaleRotation: 'Scale & Rotation',
    rotation3d: '3D Rotation',
    quickPresets: 'Quick Presets',
    zoomOut: 'Zoom Out',
    normal: 'Normal',
    zoomIn: 'Zoom In',
    scale: 'Scale',
    rotate: 'Rotate',
    rotateX: 'Rotate X',
    rotateY: 'Rotate Y'
  },
  codeView: {
    htmlExport: 'HTML Export',
    currentSlide: 'Current Slide',
    fullDocument: 'Full Document',
    copy: 'Copy',
    htmlExportDesc: 'This is how your presentation will look when exported as HTML with Impress.js',
    currentSlideDesc: 'JSON structure of the currently selected slide',
    fullDocumentDesc: 'Complete presentation data structure'
  },
  presenter: {
    title: 'Presenter View',
    start: 'Start',
    pause: 'Pause',
    reset: 'Reset',
    currentSlide: 'Current Slide',
    nextSlide: 'Next Slide',
    speakerNotes: 'Speaker Notes',
    notesPlaceholder: 'Add speaker notes for this slide...',
    previous: 'Previous',
    next: 'Next',
    first: 'First',
    last: 'Last',
    endOfPresentation: 'End of presentation'
  },
  layout: {
    chooseLayout: 'Choose Layout',
    blank: 'Blank',
    titleOnly: 'Title Only',
    titleContent: 'Title and Content',
    twoColumn: 'Two Column',
    titleImage: 'Title and Image'
  },
  theme: {
    chooseTheme: 'Choose Theme',
    default: 'Default',
    dark: 'Dark',
    minimal: 'Minimal',
    vibrant: 'Vibrant'
  },
  toolbar: {
    newSlide: 'New Slide',
    copy: 'Copy',
    delete: 'Delete',
    insertText: 'Insert Text',
    insertShape: 'Insert Shape',
    preview: 'Preview'
  },
  preview: {
    noSlides: 'No slides to preview',
    helpHint: 'Press ESC to exit, Space/Arrow keys to navigate, F to toggle fullscreen'
  },
  common: {
    ok: 'OK',
    cancel: 'Cancel',
    save: 'Save',
    close: 'Close',
    edit: 'Edit',
    doubleClickToEdit: 'Double-click to edit'
  }
} as const;

