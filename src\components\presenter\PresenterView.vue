<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'

const store = usePresentationStore()
const { doc, currentSlide } = storeToRefs(store)
const { t } = useI18n()

const startTime = ref<number>(0)
const currentTime = ref<number>(0)
const timer = ref<number | null>(null)

const currentIndex = computed(() => {
  if (!currentSlide.value) return -1
  return doc.value.slides.findIndex(s => s.id === currentSlide.value!.id)
})

const nextSlide = computed(() => {
  const idx = currentIndex.value
  if (idx < 0 || idx >= doc.value.slides.length - 1) return null
  return doc.value.slides[idx + 1]
})

const elapsedTime = computed(() => {
  if (startTime.value === 0) return '00:00'
  const elapsed = Math.floor((currentTime.value - startTime.value) / 1000)
  const minutes = Math.floor(elapsed / 60)
  const seconds = elapsed % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

const slideNotes = computed({
  get: () => currentSlide.value?.notes || '',
  set: (value) => {
    if (currentSlide.value) {
      store.updateSlideNotes(currentSlide.value.id, value)
    }
  }
})

function startTimer() {
  if (startTime.value === 0) {
    startTime.value = Date.now()
  }
  if (timer.value) return
  timer.value = setInterval(() => {
    currentTime.value = Date.now()
  }, 1000)
}

function stopTimer() {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

function resetTimer() {
  stopTimer()
  startTime.value = 0
  currentTime.value = 0
}

function goNext() {
  if (nextSlide.value) {
    store.selectSlide(nextSlide.value.id)
  }
}

function goPrev() {
  const idx = currentIndex.value
  if (idx > 0) {
    store.selectSlide(doc.value.slides[idx - 1].id)
  }
}

onMounted(() => {
  currentTime.value = Date.now()
})

onBeforeUnmount(() => {
  stopTimer()
})
</script>

<template>
  <div class="presenter-view">
    <div class="header">
      <div class="title">{{ doc.name }} - {{ t('presenter.title') }}</div>
      <div class="controls">
        <button @click="startTimer" :disabled="timer !== null">{{ t('presenter.start') }}</button>
        <button @click="stopTimer" :disabled="timer === null">{{ t('presenter.pause') }}</button>
        <button @click="resetTimer">{{ t('presenter.reset') }}</button>
        <div class="timer">{{ elapsedTime }}</div>
      </div>
    </div>

    <div class="main">
      <div class="slides-section">
        <div class="current-slide">
          <div class="slide-header">
            {{ t('presenter.currentSlide') }} ({{ currentIndex + 1 }}/{{ doc.slides.length }})
          </div>
          <div class="slide-preview large" v-if="currentSlide">
            <div class="slide-title">{{ currentSlide.title }}</div>
            <div class="slide-elements">
              <div 
                v-for="el in currentSlide.elements" 
                :key="el.id" 
                class="element-preview"
                :style="{
                  left: (el.frame.x / 10) + 'px',
                  top: (el.frame.y / 10) + 'px',
                  width: (el.frame.width / 10) + 'px',
                  height: (el.frame.height / 10) + 'px'
                }"
              >
                <div v-if="el.type === 'text'" class="text-element">T</div>
                <div v-else-if="el.type === 'shape'" class="shape-element"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="next-slide">
          <div class="slide-header">{{ t('presenter.nextSlide') }}</div>
          <div class="slide-preview" v-if="nextSlide">
            <div class="slide-title">{{ nextSlide.title }}</div>
            <div class="slide-elements">
              <div 
                v-for="el in nextSlide.elements" 
                :key="el.id" 
                class="element-preview"
                :style="{
                  left: (el.frame.x / 15) + 'px',
                  top: (el.frame.y / 15) + 'px',
                  width: (el.frame.width / 15) + 'px',
                  height: (el.frame.height / 15) + 'px'
                }"
              >
                <div v-if="el.type === 'text'" class="text-element">T</div>
                <div v-else-if="el.type === 'shape'" class="shape-element"></div>
              </div>
            </div>
          </div>
          <div v-else class="no-next">{{ t('presenter.endOfPresentation') }}</div>
        </div>
      </div>

      <div class="notes-section">
        <div class="notes-header">{{ t('presenter.speakerNotes') }}</div>
        <textarea
          v-model="slideNotes"
          class="notes-input"
          :placeholder="t('presenter.notesPlaceholder')"
        ></textarea>
      </div>
    </div>

    <div class="footer">
      <div class="navigation">
        <button @click="goPrev" :disabled="currentIndex <= 0">← {{ t('presenter.previous') }}</button>
        <button @click="goNext" :disabled="!nextSlide">{{ t('presenter.next') }} →</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.presenter-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg);
  color: var(--fg);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: var(--surface);
  border-bottom: 1px solid var(--border);
}

.title {
  font-weight: 600;
  font-size: 16px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timer {
  font-family: 'Courier New', monospace;
  font-size: 18px;
  font-weight: 600;
  color: var(--accent, #f59e0b);
  min-width: 60px;
}

.main {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  padding: 20px;
  min-height: 0;
}

.slides-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.current-slide {
  flex: 2;
}

.next-slide {
  flex: 1;
}

.slide-header {
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--muted);
}

.slide-preview {
  background: white;
  border: 1px solid var(--border);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.slide-preview.large {
  height: 300px;
}

.slide-preview:not(.large) {
  height: 150px;
}

.slide-title {
  position: absolute;
  top: 8px;
  left: 8px;
  font-size: 10px;
  color: #666;
  background: rgba(255,255,255,0.9);
  padding: 2px 6px;
  border-radius: 4px;
}

.slide-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.element-preview {
  position: absolute;
  border: 1px solid #ddd;
  border-radius: 2px;
}

.text-element {
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: #666;
}

.shape-element {
  background: #e0e0e0;
}

.no-next {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: var(--muted);
  font-style: italic;
}

.notes-section {
  display: flex;
  flex-direction: column;
}

.notes-header {
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--muted);
}

.notes-input {
  flex: 1;
  background: var(--surface);
  color: var(--fg);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 12px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  min-height: 200px;
}

.footer {
  padding: 12px 20px;
  background: var(--surface);
  border-top: 1px solid var(--border);
}

.navigation {
  display: flex;
  justify-content: center;
  gap: 12px;
}

button {
  background: var(--surface-elev);
  color: var(--fg);
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s;
}

button:hover:not(:disabled) {
  background: var(--border);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
