export interface Theme {
  id: string
  name: string
  nameZh: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    textSecondary: string
  }
  fonts: {
    heading: string
    body: string
  }
}

export const themes: Theme[] = [
  {
    id: 'default',
    name: 'Default',
    nameZh: '默认',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280'
    },
    fonts: {
      heading: 'system-ui, -apple-system, sans-serif',
      body: 'system-ui, -apple-system, sans-serif'
    }
  },
  {
    id: 'dark',
    name: 'Dark',
    nameZh: '深色',
    colors: {
      primary: '#60a5fa',
      secondary: '#94a3b8',
      accent: '#fbbf24',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
      textSecondary: '#cbd5e1'
    },
    fonts: {
      heading: 'system-ui, -apple-system, sans-serif',
      body: 'system-ui, -apple-system, sans-serif'
    }
  },
  {
    id: 'minimal',
    name: 'Minimal',
    nameZh: '简约',
    colors: {
      primary: '#000000',
      secondary: '#666666',
      accent: '#ff6b6b',
      background: '#ffffff',
      surface: '#fafafa',
      text: '#000000',
      textSecondary: '#666666'
    },
    fonts: {
      heading: 'Georgia, serif',
      body: 'system-ui, -apple-system, sans-serif'
    }
  },
  {
    id: 'vibrant',
    name: 'Vibrant',
    nameZh: '活力',
    colors: {
      primary: '#8b5cf6',
      secondary: '#06b6d4',
      accent: '#f59e0b',
      background: '#fefefe',
      surface: '#f0f9ff',
      text: '#1e1b4b',
      textSecondary: '#475569'
    },
    fonts: {
      heading: 'Inter, system-ui, sans-serif',
      body: 'Inter, system-ui, sans-serif'
    }
  }
]

export function applyTheme(themeId: string) {
  const theme = themes.find(t => t.id === themeId)
  if (!theme) return

  const root = document.documentElement
  root.style.setProperty('--theme-primary', theme.colors.primary)
  root.style.setProperty('--theme-secondary', theme.colors.secondary)
  root.style.setProperty('--theme-accent', theme.colors.accent)
  root.style.setProperty('--theme-background', theme.colors.background)
  root.style.setProperty('--theme-surface', theme.colors.surface)
  root.style.setProperty('--theme-text', theme.colors.text)
  root.style.setProperty('--theme-text-secondary', theme.colors.textSecondary)
  root.style.setProperty('--theme-font-heading', theme.fonts.heading)
  root.style.setProperty('--theme-font-body', theme.fonts.body)
}

export function getDefaultElementStyle(type: string, themeId: string = 'default') {
  const theme = themes.find(t => t.id === themeId) || themes[0]
  
  if (type === 'text') {
    return {
      fontSize: 18,
      fill: theme.colors.text,
      textAlign: 'left',
      fontFamily: theme.fonts.body
    }
  } else if (type === 'shape') {
    return {
      fill: theme.colors.surface,
      stroke: theme.colors.primary
    }
  }
  
  return {}
}
