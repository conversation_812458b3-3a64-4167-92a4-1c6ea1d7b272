<script setup lang="ts">
import InspectorPanel from '../inspector/InspectorPanel.vue'
import TextStylePanel from '../inspector/TextStylePanel.vue'
import ImpressPanel from '../inspector/ImpressPanel.vue'
const props = defineProps<{ title: string }>()
</script>

<template>
  <aside class="inspector">
    <div class="title">{{ props.title }}</div>
    <div class="content">
      <InspectorPanel />
      <TextStylePanel />
      <ImpressPanel />
    </div>
  </aside>
</template>

<style scoped>
.inspector { border-left: 1px solid var(--border); background: var(--surface); min-width: 0; display: flex; flex-direction: column; }
.title { padding: 8px 12px; font-weight: 600; border-bottom: 1px solid var(--border); }
.content { flex: 1; overflow: auto; padding: 8px; display: flex; flex-direction: column; gap: 12px; }
</style>

