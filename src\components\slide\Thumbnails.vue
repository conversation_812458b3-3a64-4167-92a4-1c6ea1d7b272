<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useI18n } from '../../i18n'
import { usePresentationStore } from '../../stores/presentation'
import ContextMenu from './ContextMenu.vue'
import LayoutPicker from './LayoutPicker.vue'

const store = usePresentationStore()
const { doc } = storeToRefs(store)
const { t } = useI18n()

const dragIndex = ref<number | null>(null)
const menu = ref<{ x: number, y: number, id: string } | null>(null)
const layoutPicker = ref<{ x: number, y: number, id: string } | null>(null)

function onDragStart(e: DragEvent, index: number) {
  dragIndex.value = index
  e.dataTransfer?.setData('text/plain', String(index))
}
function onDragOver(e: DragEvent) { e.preventDefault() }
function onDrop(e: DragEvent, index: number) {
  e.preventDefault()
  const from = dragIndex.value ?? Number(e.dataTransfer?.getData('text/plain') || -1)
  if (from >= 0) store.moveSlide(from, index)
  dragIndex.value = null
}

function onContextMenu(e: MouseEvent, id: string) {
  e.preventDefault()
  menu.value = { x: e.clientX, y: e.clientY, id }
}
function onMenuSelect(key: string) {
  if (!menu.value) return
  const id = menu.value.id
  if (key === 'new') store.addSlide(id)
  else if (key === 'dup') store.duplicateSlide(id)
  else if (key === 'del') store.removeSlide(id)
  else if (key === 'hide') store.toggleHideSlide(id)
  else if (key === 'rename') {
    const s = store.doc.slides.find(x => x.id === id)
    const name = window.prompt(t('sidebar.menu.rename'), s?.title || '')
    if (name) { if (s) s.title = name }
  } else if (key === 'layout') {
    layoutPicker.value = { x: menu.value.x, y: menu.value.y, id }
  }
  menu.value = null
}

function onLayoutSelect(layoutId: string) {
  if (!layoutPicker.value) return
  store.applyLayoutToSlide(layoutPicker.value.id, layoutId)
  layoutPicker.value = null
}
</script>

<template>
  <div class="thumbs">
    <div
      v-for="(s, i) in doc.slides"
      :key="s.id"
      class="thumb"
      :class="{ active: s.id === doc.currentSlideId, hidden: s.hidden }"
      draggable="true"
      @dragstart="onDragStart($event, i)"
      @dragover="onDragOver"
      @drop="onDrop($event, i)"
      @click="store.selectSlide(s.id)"
      @contextmenu="onContextMenu($event, s.id)"
    >
      <div class="no">{{ i + 1 }}</div>
      <div class="title">{{ s.title }}</div>
    </div>

    <ContextMenu
      v-if="menu"
      :x="menu.x"
      :y="menu.y"
      :items="[
        { key: 'new', label: t('sidebar.menu.new') },
        { key: 'dup', label: t('sidebar.menu.duplicate') },
        { key: 'del', label: t('sidebar.menu.delete') },
        { key: 'hide', label: t('sidebar.menu.hide') },
        { key: 'layout', label: t('sidebar.menu.layout') },
      ]"
      @select="onMenuSelect"
      @close="menu = null"
    />

    <LayoutPicker
      v-if="layoutPicker"
      :x="layoutPicker.x"
      :y="layoutPicker.y"
      @select="onLayoutSelect"
      @close="layoutPicker = null"
    />
  </div>
</template>

<style scoped>
.thumbs { display: flex; flex-direction: column; gap: 8px; }
.thumb { border: 1px solid var(--border); border-radius: 8px; padding: 8px; cursor: pointer; background: var(--surface-elev); }
.thumb.active { outline: 2px solid #3b82f6; }
.thumb.hidden { opacity: 0.5; }
.no { font-weight: 600; font-size: 12px; color: var(--muted); }
.title { font-size: 12px; }
</style>

