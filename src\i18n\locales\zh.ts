export default {
  app: {
    title: '演示文稿',
    theme: '主题',
    language: '语言',
    light: '浅色',
    dark: '深色',
    system: '跟随系统',
    export: '导出',
    import: '导入',
    exportJSON: '导出 JSON',
    exportHTML: '导出 HTML',
    importJSON: '导入 JSON',
    presenterView: '演讲者视图',
    grid: '网格'
  },
  sidebar: {
    title: '幻灯片',
    menu: {
      new: '新建幻灯片',
      duplicate: '复制',
      delete: '删除',
      hide: '隐藏/取消隐藏',
      rename: '重命名',
      layout: '更改版式'
    }
  },
  canvas: {
    placeholder: '画布（稍后集成 Fabric.js）',
    canvas: '画布',
    sourceCode: '源代码'
  },
  inspector: {
    title: '属性检查器',
    geometry: '几何属性',
    textStyle: '文本样式',
    position: '位置与过渡',
    noSelection: '未选择对象'
  },
  textStyle: {
    size: '字号',
    color: '颜色',
    align: '对齐',
    left: '左对齐',
    center: '居中',
    right: '右对齐'
  },
  impress: {
    position2d: '2D 位置',
    scaleRotation: '缩放与旋转',
    rotation3d: '3D 旋转',
    quickPresets: '快速预设',
    zoomOut: '缩小',
    normal: '正常',
    zoomIn: '放大',
    scale: '缩放',
    rotate: '旋转',
    rotateX: 'X 轴旋转',
    rotateY: 'Y 轴旋转'
  },
  codeView: {
    htmlExport: 'HTML 导出',
    currentSlide: '当前幻灯片',
    fullDocument: '完整文档',
    copy: '复制',
    htmlExportDesc: '这是您的演示文稿导出为 HTML 时的样子（包含 Impress.js）',
    currentSlideDesc: '当前选中幻灯片的 JSON 结构',
    fullDocumentDesc: '完整演示文稿数据结构'
  },
  presenter: {
    title: '演讲者视图',
    start: '开始',
    pause: '暂停',
    reset: '重置',
    currentSlide: '当前幻灯片',
    nextSlide: '下一张',
    speakerNotes: '演讲者备注',
    notesPlaceholder: '为此幻灯片添加演讲者备注...',
    previous: '上一张',
    next: '下一张',
    first: '第一张',
    last: '最后一张',
    endOfPresentation: '演示结束'
  },
  layout: {
    chooseLayout: '选择版式',
    blank: '空白',
    titleOnly: '仅标题',
    titleContent: '标题和内容',
    twoColumn: '两栏',
    titleImage: '标题和图片'
  },
  theme: {
    chooseTheme: '选择主题',
    default: '默认',
    dark: '深色',
    minimal: '简约',
    vibrant: '活力'
  },
  toolbar: {
    newSlide: '新建幻灯片',
    copy: '复制',
    delete: '删除',
    insertText: '插入文本',
    insertShape: '插入形状',
    preview: '预览'
  },
  preview: {
    noSlides: '没有幻灯片可预览',
    helpHint: '按 ESC 退出，空格/方向键导航，F 切换全屏'
  },
  common: {
    ok: '确定',
    cancel: '取消',
    save: '保存',
    close: '关闭',
    edit: '编辑',
    doubleClickToEdit: '双击编辑'
  }
} as const;

