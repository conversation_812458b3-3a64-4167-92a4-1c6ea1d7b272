import type { SlideElement } from '../types/presentation'

export interface Layout {
  id: string
  name: string
  nameZh: string
  elements: Omit<SlideElement, 'id'>[]
}

function uid(prefix = 'el'): string {
  return `${prefix}_${Math.random().toString(36).slice(2, 9)}`
}

export const layouts: Layout[] = [
  {
    id: 'blank',
    name: 'Blank',
    nameZh: '空白',
    elements: []
  },
  {
    id: 'title-only',
    name: 'Title Only',
    nameZh: '仅标题',
    elements: [
      {
        type: 'text',
        frame: { x: 100, y: 200, width: 800, height: 100 },
        data: { html: '<h1>Click to edit title</h1>' },
        style: { fontSize: 48, fill: '#1f2328', textAlign: 'center' }
      }
    ]
  },
  {
    id: 'title-content',
    name: 'Title and Content',
    nameZh: '标题和内容',
    elements: [
      {
        type: 'text',
        frame: { x: 100, y: 100, width: 800, height: 80 },
        data: { html: '<h1>Click to edit title</h1>' },
        style: { fontSize: 36, fill: '#1f2328', textAlign: 'left' }
      },
      {
        type: 'text',
        frame: { x: 100, y: 220, width: 800, height: 400 },
        data: { html: '<p>Click to edit content</p>' },
        style: { fontSize: 18, fill: '#1f2328', textAlign: 'left' }
      }
    ]
  },
  {
    id: 'two-column',
    name: 'Two Column',
    nameZh: '两栏',
    elements: [
      {
        type: 'text',
        frame: { x: 100, y: 100, width: 800, height: 80 },
        data: { html: '<h1>Click to edit title</h1>' },
        style: { fontSize: 36, fill: '#1f2328', textAlign: 'center' }
      },
      {
        type: 'text',
        frame: { x: 100, y: 220, width: 380, height: 400 },
        data: { html: '<p>Left column content</p>' },
        style: { fontSize: 18, fill: '#1f2328', textAlign: 'left' }
      },
      {
        type: 'text',
        frame: { x: 520, y: 220, width: 380, height: 400 },
        data: { html: '<p>Right column content</p>' },
        style: { fontSize: 18, fill: '#1f2328', textAlign: 'left' }
      }
    ]
  },
  {
    id: 'title-image',
    name: 'Title and Image',
    nameZh: '标题和图片',
    elements: [
      {
        type: 'text',
        frame: { x: 100, y: 100, width: 800, height: 80 },
        data: { html: '<h1>Click to edit title</h1>' },
        style: { fontSize: 36, fill: '#1f2328', textAlign: 'center' }
      },
      {
        type: 'shape',
        frame: { x: 250, y: 220, width: 500, height: 300 },
        data: { kind: 'rect' },
        style: { fill: '#f3f4f6', stroke: '#d1d5db' }
      }
    ]
  }
]

export function applyLayout(layoutId: string): SlideElement[] {
  const layout = layouts.find(l => l.id === layoutId)
  if (!layout) return []
  
  return layout.elements.map(el => ({
    ...el,
    id: uid('el')
  }))
}
