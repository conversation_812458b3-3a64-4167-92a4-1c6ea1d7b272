<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from './i18n'
import { usePresentationStore } from './stores/presentation'
import Toolbar from './components/layout/Toolbar.vue'
import Sidebar from './components/layout/Sidebar.vue'
import CanvasArea from './components/layout/CanvasArea.vue'
import Inspector from './components/layout/Inspector.vue'
import PresenterView from './components/presenter/PresenterView.vue'

const { t, locale, setLocale } = useI18n()
const lang = computed({ get: () => locale.value, set: (l) => setLocale(l as any) })
const store = usePresentationStore()

// Check if this is presenter view
const isPresenterView = new URLSearchParams(window.location.search).has('presenter')

function onKey(e: KeyboardEvent) {
  const mod = e.ctrlKey || e.metaKey
  if (mod && e.key.toLowerCase() === 'z') { e.preventDefault(); store.undoAction(); return }
  if (mod && e.key.toLowerCase() === 'y') { e.preventDefault(); store.redoAction(); return }
  if (e.key === 'Delete') { e.preventDefault(); store.deleteSelectedElement(); return }
}

onMounted(() => document.addEventListener('keydown', onKey))
onBeforeUnmount(() => document.removeEventListener('keydown', onKey))
</script>

<template>
  <div class="app-root">
    <PresenterView v-if="isPresenterView" />
    <template v-else>
      <Toolbar :title="t('app.title')" v-model:lang="lang" />
      <div class="layout">
        <Sidebar :title="t('sidebar.title')" />
        <CanvasArea />
        <Inspector :title="t('inspector.title')" />
      </div>
    </template>
  </div>
</template>

<style scoped>
.app-root { display: flex; flex-direction: column; height: 100vh; }
.layout { flex: 1; display: grid; grid-template-columns: 260px 1fr 320px; min-height: 0; }
</style>
