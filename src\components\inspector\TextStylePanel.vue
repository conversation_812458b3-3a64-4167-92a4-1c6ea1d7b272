<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'

const store = usePresentationStore()
const { selectedElement } = storeToRefs(store)
const { t } = useI18n()

const fontSize = computed(() => {
  const style = selectedElement.value?.style as any
  return Number(style?.fontSize) || 18
})

const textColor = computed(() => {
  const style = selectedElement.value?.style as any
  return String(style?.fill) || '#e5e7eb'
})

const textAlign = computed(() => {
  const style = selectedElement.value?.style as any
  return String(style?.textAlign) || 'left'
})

function updateFontSize(e: Event) {
  if (!selectedElement.value) return
  store.updateElementStyle(selectedElement.value.id, { fontSize: Number((e.target as HTMLInputElement).value) })
}

function updateColor(e: Event) {
  if (!selectedElement.value) return
  store.updateElementStyle(selectedElement.value.id, { fill: String((e.target as HTMLInputElement).value) })
}

function updateAlign(e: Event) {
  if (!selectedElement.value) return
  store.updateElementStyle(selectedElement.value.id, { textAlign: String((e.target as HTMLSelectElement).value) })
}
</script>

<template>
  <div v-if="selectedElement && selectedElement.type==='text'" class="panel">
    <div class="panel-title">{{ t('inspector.textStyle') }}</div>
    <div class="row">
      <label>{{ t('textStyle.size') }}</label>
      <input type="number" :value="fontSize" @change="updateFontSize"/>
    </div>
    <div class="row">
      <label>{{ t('textStyle.color') }}</label>
      <input type="color" :value="textColor" @change="updateColor"/>
    </div>
    <div class="row">
      <label>{{ t('textStyle.align') }}</label>
      <select :value="textAlign" @change="updateAlign">
        <option value="left">{{ t('textStyle.left') }}</option>
        <option value="center">{{ t('textStyle.center') }}</option>
        <option value="right">{{ t('textStyle.right') }}</option>
      </select>
    </div>
  </div>
</template>

<style scoped>
.panel { display: flex; flex-direction: column; gap: 8px; padding: 8px; }
.panel-title { font-weight: 600; font-size: 14px; margin-bottom: 8px; color: var(--fg); }
.row { display: grid; grid-template-columns: 48px 1fr; gap: 8px; align-items: center; }
input, select { background: var(--surface-elev); color: var(--fg); border: 1px solid var(--border); border-radius: 6px; padding: 4px 6px; }
</style>

