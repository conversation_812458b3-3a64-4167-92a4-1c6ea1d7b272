import { defineStore } from 'pinia'
import type { Presentation, Slide, SlideElement, ElementType } from '../types/presentation'
import { exportToJSON, importFromJSON, exportToHTML, downloadFile } from '../utils/export'
import { applyLayout } from '../utils/layouts'
import { applyTheme, getDefaultElementStyle } from '../utils/themes'

function uid(prefix = 'id'): string {
  return `${prefix}_${Math.random().toString(36).slice(2, 9)}`
}

function createEmptyPresentation(): Presentation {
  const firstSlide: Slide = {
    id: uid('slide'),
    title: 'Slide 1',
    elements: [],
    impress: { x: 0, y: 0, scale: 1 },
  }
  return {
    id: uid('ppt'),
    name: 'Untitled',
    slides: [firstSlide],
    currentSlideId: firstSlide.id,
    theme: 'default',
    createdAt: Date.now(),
    updatedAt: Date.now()
  }
}

export const usePresentationStore = defineStore('presentation', {
  state: () => ({
    doc: createEmptyPresentation(),
    selectedElementId: null as string | null,
    undoStack: [] as Presentation[],
    redoStack: [] as Presentation[],
    ui: { grid: true }
  }),
  getters: {
    currentSlide(state): Slide | null {
      return state.doc.slides.find(s => s.id === state.doc.currentSlideId) ?? null
    },
    selectedElement(state): SlideElement | null {
      const slide = (state as any).currentSlide as Slide | null
      if (!slide || !state.selectedElementId) return null
      return slide.elements.find(e => e.id === state.selectedElementId) ?? null
    }
  },
  actions: {
    touch() {
      this.doc.updatedAt = Date.now()
      this.autoSave()
    },
    autoSave() {
      try {
        localStorage.setItem('presentation-autosave', JSON.stringify(this.doc))
      } catch (e) {
        console.warn('Failed to auto-save:', e)
      }
    },
    loadAutoSave() {
      try {
        const saved = localStorage.getItem('presentation-autosave')
        if (saved) {
          const doc = JSON.parse(saved)
          if (doc.id && doc.name && Array.isArray(doc.slides)) {
            this.doc = doc
            if (this.doc.theme) {
              applyTheme(this.doc.theme)
            }
            return true
          }
        }
      } catch (e) {
        console.warn('Failed to load auto-save:', e)
      }
      return false
    },
    snapshot() {
      // cap to 30 snapshots
      this.undoStack.push(JSON.parse(JSON.stringify(this.doc)))
      if (this.undoStack.length > 30) this.undoStack.shift()
      // clear redo on new change
      this.redoStack = []
    },
    undoAction() {
      const prev = this.undoStack.pop()
      if (!prev) return
      this.redoStack.push(JSON.parse(JSON.stringify(this.doc)))
      this.doc = JSON.parse(JSON.stringify(prev))
    },
    redoAction() {
      const next = this.redoStack.pop()
      if (!next) return
      this.undoStack.push(JSON.parse(JSON.stringify(this.doc)))
      this.doc = JSON.parse(JSON.stringify(next))
    },
    selectSlide(id: string) {
      this.doc.currentSlideId = id
      this.touch()
    },
    addSlide(afterId?: string, layoutId: string = 'blank') {
      this.snapshot()
      const idx = afterId ? this.doc.slides.findIndex(s => s.id === afterId) : this.doc.slides.length - 1
      const elements = applyLayout(layoutId)
      const slide: Slide = {
        id: uid('slide'),
        title: `Slide ${this.doc.slides.length + 1}`,
        elements,
        impress: { x: (this.doc.slides.length)*1200, y: 0, scale: 1 }
      }
      if (idx >= 0) this.doc.slides.splice(idx + 1, 0, slide)
      else this.doc.slides.push(slide)
      this.doc.currentSlideId = slide.id
      this.touch()
    },
    applyLayoutToSlide(slideId: string, layoutId: string) {
      this.snapshot()
      const slide = this.doc.slides.find(s => s.id === slideId)
      if (!slide) return
      slide.elements = applyLayout(layoutId)
      this.touch()
    },
    removeSlide(id: string) {
      this.snapshot()
      const i = this.doc.slides.findIndex(s => s.id === id)
      if (i < 0) return
      this.doc.slides.splice(i, 1)
      if (this.doc.currentSlideId === id) this.doc.currentSlideId = this.doc.slides[i]?.id ?? this.doc.slides[i-1]?.id ?? null
      this.touch()
    },
    duplicateSlide(id: string) {
      this.snapshot()
      const i = this.doc.slides.findIndex(s => s.id === id)
      if (i < 0) return
      const copy: Slide = JSON.parse(JSON.stringify(this.doc.slides[i]))
      copy.id = uid('slide')
      copy.title += ' (Copy)'
      this.doc.slides.splice(i + 1, 0, copy)
      this.doc.currentSlideId = copy.id
      this.touch()
    },
    moveSlide(fromIndex: number, toIndex: number) {
      if (fromIndex === toIndex) return
      this.snapshot()
      const arr = this.doc.slides
      if (fromIndex < 0 || toIndex < 0 || fromIndex >= arr.length || toIndex >= arr.length) return
      const [s] = arr.splice(fromIndex, 1)
      arr.splice(toIndex, 0, s)
      this.touch()
    },
    toggleHideSlide(id: string) {
      this.snapshot()
      const s = this.doc.slides.find(x => x.id === id)
      if (s) s.hidden = !s.hidden
      this.touch()
    },
    insertElement(type: ElementType) {
      this.snapshot()
      const slide = this.currentSlide
      if (!slide) return
      const base = { id: uid('el'), type, frame: { x: 100, y: 100, width: 240, height: 120 } }
      const defaultStyle = getDefaultElementStyle(type, this.doc.theme)

      if (type === 'text') {
        slide.elements.push({ ...base, data: { html: '<p>Double-click to edit</p>' }, style: defaultStyle })
      } else if (type === 'shape') {
        slide.elements.push({ ...base, data: { kind: 'rect' } as any, style: defaultStyle })
      } else if (type === 'image') {
        slide.elements.push({ ...base, data: { src: '' } })
      }
      this.touch()
    },
    setTheme(themeId: string) {
      this.snapshot()
      this.doc.theme = themeId
      applyTheme(themeId)
      this.touch()
    },
    updateSlideImpress(slideId: string, impress: any) {
      this.snapshot()
      const slide = this.doc.slides.find(s => s.id === slideId)
      if (!slide) return
      slide.impress = { ...slide.impress, ...impress }
      this.touch()
    },
    updateSlideNotes(slideId: string, notes: string) {
      const slide = this.doc.slides.find(s => s.id === slideId)
      if (!slide) return
      slide.notes = notes
      this.touch()
    },
    deleteSelectedElement() {
      this.snapshot()
      const slide = this.currentSlide
      if (!slide || !this.selectedElementId) return
      const i = slide.elements.findIndex(e => e.id === this.selectedElementId)
      if (i >= 0) slide.elements.splice(i, 1)
      this.selectedElementId = null
      this.touch()
    },
    updateElementFrame(id: string, frame: Partial<SlideElement['frame']>) {
      this.snapshot()
      const slide = this.currentSlide
      if (!slide) return
      const el = slide.elements.find(e => e.id === id)
      if (!el) return
      el.frame = { ...el.frame, ...frame }
      this.touch()
    },
    updateElementHtml(id: string, html: string) {
      this.snapshot()
      const slide = this.currentSlide
      if (!slide) return
      const el = slide.elements.find(e => e.id === id)
      if (!el || el.type !== 'text') return
      ;(el.data as any).html = html
      this.touch()
    },
    updateElementStyle(id: string, style: Record<string, any>) {
      this.snapshot()
      const slide = this.currentSlide
      if (!slide) return
      const el = slide.elements.find(e => e.id === id)
      if (!el) return
      el.style = { ...(el.style || {}), ...style }
      this.touch()
    },
    exportJSON() {
      const json = exportToJSON(this.doc)
      downloadFile(json, `${this.doc.name}.json`, 'application/json')
    },
    async exportHTML() {
      const html = await exportToHTML(this.doc)
      downloadFile(html, `${this.doc.name}.html`, 'text/html')
    },
    importJSON() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (!file) return
        const reader = new FileReader()
        reader.onload = (e) => {
          const content = e.target?.result as string
          const imported = importFromJSON(content)
          if (imported) {
            this.doc = imported
            this.selectedElementId = null
            this.undoStack = []
            this.redoStack = []
            this.touch()
          } else {
            alert('Failed to import file. Please check the format.')
          }
        }
        reader.readAsText(file)
      }
      input.click()
    }
  }
})

