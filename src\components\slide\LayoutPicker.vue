<script setup lang="ts">
import { layouts } from '../../utils/layouts'
import { useI18n } from '../../i18n'

const props = defineProps<{ x: number, y: number }>()
const emit = defineEmits<{ select: [string], close: [] }>()

const { t, locale } = useI18n()

function onDocClick(e: MouseEvent) {
  const target = e.target as HTMLElement
  if (!target.closest('.layout-picker')) {
    emit('close')
  }
}

function onSelect(layoutId: string) {
  emit('select', layoutId)
}
</script>

<template>
  <div class="layout-picker" :style="{ left: props.x + 'px', top: props.y + 'px' }" @mousedown.stop>
    <div class="title">{{ t('layout.chooseLayout') }}</div>
    <div class="layouts">
      <div 
        v-for="layout in layouts" 
        :key="layout.id" 
        class="layout-item"
        @click="onSelect(layout.id)"
      >
        <div class="layout-preview">
          <div v-for="(el, i) in layout.elements" :key="i" class="preview-element" :class="el.type">
            <div v-if="el.type === 'text'" class="text-preview"></div>
            <div v-else-if="el.type === 'shape'" class="shape-preview"></div>
          </div>
        </div>
        <div class="layout-name">{{ locale.value === 'zh' ? layout.nameZh : layout.name }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.layout-picker {
  position: fixed;
  z-index: 1001;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0,0,0,.15);
  padding: 12px;
  min-width: 300px;
}

.title {
  font-weight: 600;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border);
}

.layouts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.layout-item {
  padding: 8px;
  border: 1px solid var(--border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.layout-item:hover {
  border-color: #3b82f6;
  background: var(--surface-elev);
}

.layout-preview {
  width: 120px;
  height: 80px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  position: relative;
  margin-bottom: 6px;
  overflow: hidden;
}

.preview-element {
  position: absolute;
}

.preview-element.text .text-preview {
  background: #333;
  height: 8px;
  border-radius: 2px;
}

.preview-element.shape .shape-preview {
  background: #e5e7eb;
  border: 1px solid #d1d5db;
  border-radius: 2px;
  width: 100%;
  height: 100%;
}

.layout-name {
  font-size: 12px;
  text-align: center;
  color: var(--muted);
}
</style>
