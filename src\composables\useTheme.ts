type Mode = 'light' | 'dark' | 'system';

export function useTheme() {
  const key = 'theme-mode';
  const getPreferred = (): Mode => (localStorage.getItem(key) as Mode) || 'system';
  const apply = (mode: Mode) => {
    const root = document.documentElement;
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const isDark = mode === 'dark' || (mode === 'system' && prefersDark);
    root.classList.toggle('dark', isDark);
    root.dataset.theme = isDark ? 'dark' : 'light';
  };
  const setMode = (mode: Mode) => {
    localStorage.setItem(key, mode);
    apply(mode);
  };
  const init = () => apply(getPreferred());
  return { init, setMode, getPreferred };
}

