{"name": "slide", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tiptap/starter-kit": "^3.0.9", "@tiptap/vue-3": "^3.0.9", "fabric": "^6.7.1", "impress.js": "^1.1.0", "pinia": "^3.0.3", "vue": "^3.5.18"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.1.0", "vue-tsc": "^3.0.5"}}