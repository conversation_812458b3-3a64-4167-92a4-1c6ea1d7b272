export type ElementType = 'text' | 'image' | 'shape';

export interface Frame {
  x: number; y: number; width: number; height: number; rotate?: number;
}

export interface TextData { html: string; }
export interface ImageData { src: string; }
export interface ShapeData { kind: 'rect' | 'ellipse' | 'triangle'; }

export type ElementData = TextData | ImageData | ShapeData;

export interface ElementBase {
  id: string;
  type: ElementType;
  frame: Frame;
  style?: Record<string, string | number>;
}

export interface SlideElement extends ElementBase {
  data: ElementData;
}

export interface ImpressPosition {
  x?: number; y?: number; z?: number; scale?: number;
  rotate?: number; rotateX?: number; rotateY?: number;
}

export interface Slide {
  id: string;
  title: string;
  elements: SlideElement[];
  impress?: ImpressPosition;
  hidden?: boolean;
  notes?: string;
}

export interface Presentation {
  id: string;
  name: string;
  slides: Slide[];
  currentSlideId: string | null;
  theme?: string;
  createdAt: number;
  updatedAt: number;
}

