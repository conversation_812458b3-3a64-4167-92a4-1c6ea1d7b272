import type { Presentation } from '../types/presentation'

// 内联的 Impress.js CSS 样式
const impressCSS = `
/* Basic impress.js styles */
.impress-enabled .step {
  margin: 0;
  opacity: 0.3;
  transition: opacity 1s;
}

.impress-enabled .step.active {
  opacity: 1;
}

.impress-enabled .step.past {
  opacity: 0.3;
}

.impress-enabled .step.future {
  opacity: 0.3;
}

.impress-not-supported .step {
  position: relative;
  opacity: 1;
  margin: 20px auto;
}

.fallback-message {
  font-family: sans-serif;
  line-height: 1.3;
  width: 780px;
  padding: 10px 10px 0;
  margin: 20px auto;
  border: 1px solid #E4C652;
  border-radius: 10px;
  background: #EEDC94;
}

.fallback-message p {
  margin-bottom: 10px;
}

.impress-console {
  position: fixed;
  right: 1px;
  bottom: 1px;
  opacity: 0.6;
  z-index: 100;
}

.impress-console .log {
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 5px;
  margin: 1px;
  font-family: monospace;
  font-size: 12px;
  border-radius: 3px;
}
`

// 获取本地 Impress.js 内容的函数
async function getImpressJSContent(): Promise<{ js: string, css: string }> {
  try {
    // 从 public 目录读取 Impress.js 文件
    const [jsResponse, cssResponse] = await Promise.all([
      fetch('/impress.min.js'),
      fetch('/impress-demo.css')
    ])

    const js = jsResponse.ok ? await jsResponse.text() : ''
    const css = cssResponse.ok ? await cssResponse.text() : ''

    return { js, css }
  } catch (error) {
    console.warn('Failed to load local impress.js files, using fallback')
    // 返回简化的 Impress.js 和基础样式
    return {
      js: `
// Simplified Impress.js fallback
(function() {
  window.impress = function() {
    return {
      init: function() {
        console.log('Impress.js initialized (fallback mode)');
        // 基础的幻灯片导航功能
        let currentStep = 0;
        const steps = document.querySelectorAll('.step');

        function showStep(n) {
          steps.forEach((step, i) => {
            step.classList.toggle('active', i === n);
            step.classList.toggle('past', i < n);
            step.classList.toggle('future', i > n);
          });
        }

        document.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowRight' || e.key === ' ') {
            if (currentStep < steps.length - 1) {
              currentStep++;
              showStep(currentStep);
            }
          } else if (e.key === 'ArrowLeft') {
            if (currentStep > 0) {
              currentStep--;
              showStep(currentStep);
            }
          }
        });

        showStep(0);
        document.body.classList.add('impress-enabled');
        document.body.classList.remove('impress-not-supported');
      }
    };
  };
})();
`,
      css: impressCSS
    }
  }
}

export function exportToJSON(doc: Presentation): string {
  return JSON.stringify(doc, null, 2)
}

export function importFromJSON(json: string): Presentation | null {
  try {
    const data = JSON.parse(json)
    // basic validation
    if (!data.id || !data.name || !Array.isArray(data.slides)) {
      throw new Error('Invalid presentation format')
    }
    return data as Presentation
  } catch (e) {
    console.error('Failed to import JSON:', e)
    return null
  }
}

export async function exportToHTML(doc: Presentation): Promise<string> {
  const slides = doc.slides.filter(s => !s.hidden).map(slide => {
    const elements = slide.elements.map(el => {
      const style = `position: absolute; left: ${el.frame.x}px; top: ${el.frame.y}px; width: ${el.frame.width}px; height: ${el.frame.height}px; transform: rotate(${el.frame.rotate || 0}deg);`
      
      if (el.type === 'text') {
        const textStyle = el.style as any
        const fontSize = textStyle?.fontSize || 18
        const fill = textStyle?.fill || '#000'
        const textAlign = textStyle?.textAlign || 'left'
        return `<div style="${style} font-size: ${fontSize}px; color: ${fill}; text-align: ${textAlign};">${(el.data as any).html || ''}</div>`
      } else if (el.type === 'shape' && (el.data as any).kind === 'rect') {
        const shapeStyle = el.style as any
        const fill = shapeStyle?.fill || '#ccc'
        const stroke = shapeStyle?.stroke || '#000'
        return `<div style="${style} background: ${fill}; border: 1px solid ${stroke}; border-radius: 4px;"></div>`
      }
      return ''
    }).join('\n    ')

    const impressAttrs = slide.impress ? 
      `data-x="${slide.impress.x || 0}" data-y="${slide.impress.y || 0}" data-scale="${slide.impress.scale || 1}" data-rotate="${slide.impress.rotate || 0}"` : 
      'data-x="0" data-y="0" data-scale="1"'

    return `  <div class="step" ${impressAttrs}>
    ${elements}
  </div>`
  }).join('\n\n')

  // 获取本地 Impress.js 内容
  const { js: impressJSContent, css: impressCSSContent } = await getImpressJSContent()

  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>${doc.name}</title>
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:regular,semibold,italic,italicsemibold|PT+Sans:400,700,400italic,700italic|PT+Serif:400,700,400italic,700italic" rel="stylesheet">
  <style>
    body {
      font-family: 'Open Sans', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f8f9fa;
    }
    .step {
      position: relative;
      width: 1000px;
      height: 700px;
      padding: 40px;
      margin: 20px auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .step h1 {
      margin: 0 0 20px 0;
      font-size: 48px;
      font-weight: 600;
      color: #333;
    }

    /* Impress.js CSS */
    ${impressCSSContent}
  </style>
</head>
<body class="impress-not-supported">
  <div class="fallback-message">
    <p>Your browser <b>doesn't support the features required</b> by impress.js, so you are presented with a simplified version of this presentation.</p>
    <p>For the best experience please use the latest <b>Chrome</b>, <b>Safari</b> or <b>Firefox</b> browser.</p>
  </div>

  <div id="impress">
${slides}
  </div>

  <script>
${impressJSContent}
${impressJSContent ? 'impress().init();' : ''}
  </script>
</body>
</html>`
}

export function downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
