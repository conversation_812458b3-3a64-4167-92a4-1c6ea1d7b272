<script setup lang="ts">
import Thumbnails from '../slide/Thumbnails.vue'
const props = defineProps<{ title: string }>()
</script>

<template>
  <aside class="sidebar">
    <div class="title">{{ props.title }}</div>
    <div class="content">
      <Thumbnails />
    </div>
  </aside>
</template>

<style scoped>
.sidebar { border-right: 1px solid var(--border); background: var(--surface); min-width: 0; display: flex; flex-direction: column; }
.title { padding: 8px 12px; font-weight: 600; border-bottom: 1px solid var(--border); }
.content { flex: 1; overflow: auto; padding: 8px; }
</style>

