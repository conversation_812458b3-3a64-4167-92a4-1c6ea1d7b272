<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'

const store = usePresentationStore()
const { currentSlide } = storeToRefs(store)
const { t } = useI18n()

const position = computed({
  get: () => currentSlide.value?.impress || { x: 0, y: 0, z: 0, scale: 1, rotate: 0, rotateX: 0, rotateY: 0 },
  set: (value) => {
    if (!currentSlide.value) return
    store.updateSlideImpress(currentSlide.value.id, value)
  }
})

function updatePosition(key: string, value: number) {
  if (!currentSlide.value) return
  const newPos = { ...position.value, [key]: value }
  store.updateSlideImpress(currentSlide.value.id, newPos)
}

function number(v: any, d = 0) {
  return isFinite(+v) ? +v : d
}

function updateX(e: Event) { updatePosition('x', number((e.target as HTMLInputElement).value)) }
function updateY(e: Event) { updatePosition('y', number((e.target as HTMLInputElement).value)) }
function updateZ(e: Event) { updatePosition('z', number((e.target as HTMLInputElement).value)) }
function updateScale(e: Event) { updatePosition('scale', number((e.target as HTMLInputElement).value, 1)) }
function updateRotate(e: Event) { updatePosition('rotate', number((e.target as HTMLInputElement).value)) }
function updateRotateX(e: Event) { updatePosition('rotateX', number((e.target as HTMLInputElement).value)) }
function updateRotateY(e: Event) { updatePosition('rotateY', number((e.target as HTMLInputElement).value)) }
</script>

<template>
  <div v-if="currentSlide" class="panel">
    <div class="section-title">{{ t('inspector.position') }}</div>

    <div class="section">
      <div class="label">{{ t('impress.position2d') }}</div>
      <div class="row">
        <label>X</label>
        <input type="number" :value="position.x || 0" @change="updateX"/>
      </div>
      <div class="row">
        <label>Y</label>
        <input type="number" :value="position.y || 0" @change="updateY"/>
      </div>
      <div class="row">
        <label>Z</label>
        <input type="number" :value="position.z || 0" @change="updateZ"/>
      </div>
    </div>

    <div class="section">
      <div class="label">{{ t('impress.scaleRotation') }}</div>
      <div class="row">
        <label>{{ t('impress.scale') }}</label>
        <input
          type="range"
          min="0.1"
          max="5"
          step="0.1"
          :value="position.scale || 1"
          @input="updateScale"
        />
        <span class="value">{{ (position.scale || 1).toFixed(1) }}</span>
      </div>
      <div class="row">
        <label>{{ t('impress.rotate') }}</label>
        <input
          type="range"
          min="-360"
          max="360"
          step="15"
          :value="position.rotate || 0"
          @input="updateRotate"
        />
        <span class="value">{{ position.rotate || 0 }}°</span>
      </div>
    </div>

    <div class="section">
      <div class="label">{{ t('impress.rotation3d') }}</div>
      <div class="row">
        <label>{{ t('impress.rotateX') }}</label>
        <input
          type="range"
          min="-180"
          max="180"
          step="15"
          :value="position.rotateX || 0"
          @input="updateRotateX"
        />
        <span class="value">{{ position.rotateX || 0 }}°</span>
      </div>
      <div class="row">
        <label>{{ t('impress.rotateY') }}</label>
        <input
          type="range"
          min="-180"
          max="180"
          step="15"
          :value="position.rotateY || 0"
          @input="updateRotateY"
        />
        <span class="value">{{ position.rotateY || 0 }}°</span>
      </div>
    </div>

    <div class="section">
      <div class="label">{{ t('impress.quickPresets') }}</div>
      <div class="presets">
        <button @click="updatePosition('scale', 0.5)">{{ t('impress.zoomOut') }}</button>
        <button @click="updatePosition('scale', 1)">{{ t('impress.normal') }}</button>
        <button @click="updatePosition('scale', 2)">{{ t('impress.zoomIn') }}</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel { display: flex; flex-direction: column; gap: 16px; padding: 8px; }
.section-title { font-weight: 600; font-size: 14px; margin-bottom: 8px; }
.section { background: var(--surface-elev); padding: 12px; border-radius: 6px; }
.label { font-size: 12px; font-weight: 500; color: var(--muted); margin-bottom: 8px; }
.row { display: grid; grid-template-columns: 60px 1fr auto; gap: 8px; align-items: center; margin-bottom: 8px; }
.row:last-child { margin-bottom: 0; }
input[type="number"] { background: var(--surface); color: var(--fg); border: 1px solid var(--border); border-radius: 4px; padding: 4px 6px; }
input[type="range"] { flex: 1; }
.value { font-size: 12px; color: var(--muted); min-width: 40px; text-align: right; }
.presets { display: flex; gap: 4px; }
.presets button { flex: 1; padding: 4px 8px; font-size: 11px; }
</style>
