import { describe, it, expect, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia'
import { usePresentationStore } from '../presentation'

describe('Presentation Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should create empty presentation', () => {
    const store = usePresentationStore()
    expect(store.doc.name).toBe('Untitled')
    expect(store.doc.slides).toHaveLength(1)
    expect(store.doc.slides[0].title).toBe('Slide 1')
  })

  it('should add new slide', () => {
    const store = usePresentationStore()
    const initialCount = store.doc.slides.length
    
    store.addSlide()
    
    expect(store.doc.slides).toHaveLength(initialCount + 1)
    expect(store.doc.slides[1].title).toBe('Slide 2')
  })

  it('should remove slide', () => {
    const store = usePresentationStore()
    store.addSlide()
    const slideId = store.doc.slides[1].id
    
    store.removeSlide(slideId)
    
    expect(store.doc.slides).toHaveLength(1)
    expect(store.doc.slides.find(s => s.id === slideId)).toBeUndefined()
  })

  it('should duplicate slide', () => {
    const store = usePresentationStore()
    const originalSlide = store.doc.slides[0]
    
    store.duplicateSlide(originalSlide.id)
    
    expect(store.doc.slides).toHaveLength(2)
    expect(store.doc.slides[1].title).toBe('Slide 1 (Copy)')
  })

  it('should insert text element', () => {
    const store = usePresentationStore()
    
    store.insertElement('text')
    
    const slide = store.currentSlide
    expect(slide?.elements).toHaveLength(1)
    expect(slide?.elements[0].type).toBe('text')
  })

  it('should insert shape element', () => {
    const store = usePresentationStore()
    
    store.insertElement('shape')
    
    const slide = store.currentSlide
    expect(slide?.elements).toHaveLength(1)
    expect(slide?.elements[0].type).toBe('shape')
  })

  it('should update element frame', () => {
    const store = usePresentationStore()
    store.insertElement('text')
    
    const element = store.currentSlide?.elements[0]
    if (!element) throw new Error('Element not found')
    
    store.updateElementFrame(element.id, { x: 200, y: 300 })
    
    expect(element.frame.x).toBe(200)
    expect(element.frame.y).toBe(300)
  })

  it('should update element style', () => {
    const store = usePresentationStore()
    store.insertElement('text')
    
    const element = store.currentSlide?.elements[0]
    if (!element) throw new Error('Element not found')
    
    store.updateElementStyle(element.id, { fontSize: 24, fill: '#ff0000' })
    
    expect((element.style as any)?.fontSize).toBe(24)
    expect((element.style as any)?.fill).toBe('#ff0000')
  })

  it('should handle undo/redo', () => {
    const store = usePresentationStore()
    const originalName = store.doc.name
    
    // Make a change
    store.doc.name = 'Test Presentation'
    store.snapshot()
    
    // Make another change
    store.addSlide()
    const slideCount = store.doc.slides.length
    
    // Undo
    store.undoAction()
    expect(store.doc.slides.length).toBe(slideCount - 1)
    
    // Redo
    store.redoAction()
    expect(store.doc.slides.length).toBe(slideCount)
  })

  it('should set theme', () => {
    const store = usePresentationStore()
    
    store.setTheme('dark')
    
    expect(store.doc.theme).toBe('dark')
  })

  it('should move slides', () => {
    const store = usePresentationStore()
    store.addSlide()
    store.addSlide()
    
    const slide1 = store.doc.slides[0]
    const slide2 = store.doc.slides[1]
    
    store.moveSlide(0, 1)
    
    expect(store.doc.slides[0]).toBe(slide2)
    expect(store.doc.slides[1]).toBe(slide1)
  })
})
