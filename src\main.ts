import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import { createI18n, I18N_KEY } from './i18n'
import { useTheme } from './composables/useTheme'
import { createPinia } from 'pinia'
import { applyTheme } from './utils/themes'

const app = createApp(App)

// init theme
const { init } = useTheme()
init()

// init i18n
const i18n = createI18n()
app.provide(I18N_KEY as any, i18n)

// pinia
const pinia = createPinia()
app.use(pinia)

app.mount('#app')

// load auto-saved data after mounting
import { usePresentationStore } from './stores/presentation'
const store = usePresentationStore()
if (!store.loadAutoSave()) {
  // apply default theme if no auto-save
  applyTheme('default')
} else {
  // apply saved theme
  applyTheme(store.doc.theme || 'default')
}
