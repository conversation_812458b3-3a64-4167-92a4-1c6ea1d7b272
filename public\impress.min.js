/*! Licensed under MIT License - http://github.com/impress/impress.js */
!function(e,t){"use strict";var n,i,r,s,o=(i=e.createElement("dummy").style,r="Webkit Moz O ms Khtml".split(" "),s={},function(e){if(void 0===s[e]){var t=e.charAt(0).toUpperCase()+e.substr(1),n=(e+" "+r.join(t+" ")+t).split(" ");for(var o in s[e]=null,n)if(void 0!==i[n[o]]){s[e]=n[o];break}}return s[e]}),a=function(e,t){var n="xyz",i="";if("string"==typeof e)for(var r in e.split(""))n.indexOf(e[r])>=0&&(i+=e[r],n=n.split(e[r]).join(""));return i||(void 0!==t?t:"xyz")},l=function(e,t){var n,i;for(n in t)t.hasOwnProperty(n)&&null!==(i=o(n))&&(e.style[i]=t[n]);return e},d=function(e){return" translate3d("+e.x+"px,"+e.y+"px,"+e.z+"px) "},u=function(e,t){var n="",i=(e.order?e.order:"xyz").split("");t&&(i=i.reverse());for(var r=0;r<i.length;r++)n+=" rotate"+i[r].toUpperCase()+"("+e[i[r]]+"deg)";return n},c=function(e){return" scale("+e+") "},p=function(e){var n=t.innerHeight/e.height,i=t.innerWidth/e.width,r=n>i?i:n;return e.maxScale&&r>e.maxScale&&(r=e.maxScale),e.minScale&&r<e.minScale&&(r=e.minScale),r},v=e.body,m=null!==o("perspective")&&v.classList&&v.dataset;m||(v.className+=" impress-not-supported ");var f={},g=[],y=[],h=1024,b=768,E=1,L=0,x=1e3,w=1e3,k=function(){return!1},C=t.impress=function(i){if(!m)return{init:k,goto:k,prev:k,next:k,swipe:k,tear:k,lib:{}};if(f["impress-root-"+(i=i||"impress")])return f["impress-root-"+i];n=S(i),v.classList.remove("impress-not-supported"),v.classList.add("impress-supported");var r={},s=null,o=null,g=null,y=null,C=null,N=n.util.byId(i),T=e.createElement("div"),z=!1,I=null,M=function(e,t){var i=e.dataset,s={translate:{x:n.util.toNumber(i.x),y:n.util.toNumber(i.y),z:n.util.toNumber(i.z)},rotate:{x:n.util.toNumber(i.rotateX),y:n.util.toNumber(i.rotateY),z:n.util.toNumber(i.rotateZ||i.rotate),order:a(i.rotateOrder)},scale:n.util.toNumber(i.scale,1),transitionDuration:n.util.toNumber(i.transitionDuration,y.transitionDuration),el:e};e.id||(e.id="step-"+(t+1)),r["impress-"+e.id]=s,l(e,{position:"absolute",transform:"translate(-50%,-50%)"+d(s.translate)+u(s.rotate)+c(s.scale),transformStyle:"preserve-3d"})},B=function(){(g=n.util.$$(".step",N)).forEach(M)},P=null,H=function(e,i,a,m){if(a=a||"goto",m=m||null,!z)return!1;if(B(),!(e=function(e){return"number"==typeof e?e=e<0?g[g.length+e]:g[e]:"string"==typeof e&&(e=n.util.byId(e)),e&&e.id&&r["impress-"+e.id]?e:null}(e)))return!1;t.scrollTo(0,0);var f=r["impress-"+e.id];if(i=void 0!==i?i:f.transitionDuration,s&&s!==e){var h={target:s,detail:{}};if(h.detail.next=e,h.detail.transitionDuration=i,h.detail.reason=a,m&&(h.origEvent=m),!1===A(h))return!1;e=h.detail.next,f=r["impress-"+e.id],i=h.detail.transitionDuration}s&&(s.classList.remove("active"),v.classList.remove("impress-on-"+s.id)),e.classList.add("active"),v.classList.add("impress-on-"+e.id);var b={rotate:{x:-f.rotate.x,y:-f.rotate.y,z:-f.rotate.z,order:f.rotate.order},translate:{x:-f.translate.x,y:-f.translate.y,z:-f.translate.z},scale:1/f.scale},E=b.scale>=o.scale,L=(i=n.util.toNumber(i,y.transitionDuration))/2;e===s&&(C=p(y));var x,w,k=b.scale*C;return s&&s!==e&&(w=e,I===(x=s)&&(n.util.triggerEvent(x,"impress:stepleave",{next:w}),I=null)),l(N,{perspective:y.perspective/k+"px",transform:c(k),transitionDuration:i+"ms",transitionDelay:(E?L:0)+"ms"}),l(T,{transform:u(b.rotate,!0)+d(b.translate),transitionDuration:i+"ms",transitionDelay:(E?0:L)+"ms"}),(o.scale===b.scale||o.rotate.x===b.rotate.x&&o.rotate.y===b.rotate.y&&o.rotate.z===b.rotate.z&&o.translate.x===b.translate.x&&o.translate.y===b.translate.y&&o.translate.z===b.translate.z)&&(L=0),o=b,s=e,t.clearTimeout(P),P=t.setTimeout((function(){!function(e){I!==e&&(n.util.triggerEvent(e,"impress:stepenter"),I=e),n.util.triggerEvent(e,"impress:steprefresh")}(s)}),i+L),e},q=function(e,t,n){return e+(t-e)*n};return n.gc.addEventListener(N,"impress:init",(function(){g.forEach((function(e){e.classList.add("future")})),n.gc.addEventListener(N,"impress:stepenter",(function(e){e.target.classList.remove("past"),e.target.classList.remove("future"),e.target.classList.add("present")}),!1),n.gc.addEventListener(N,"impress:stepleave",(function(e){e.target.classList.remove("present"),e.target.classList.add("past")}),!1)}),!1),n.gc.addEventListener(N,"impress:init",(function(){var e="";n.gc.addEventListener(N,"impress:stepenter",(function(n){t.location.hash=e="#/"+n.target.id}),!1),n.gc.addEventListener(t,"hashchange",(function(){t.location.hash!==e&&H(n.util.getElementFromHash())}),!1),H(n.util.getElementFromHash()||g[0],0)}),!1),v.classList.add("impress-disabled"),f["impress-root-"+i]={init:function(){if(!z){D(N);var t=n.util.$("meta[name='viewport']")||e.createElement("meta");t.content="width=device-width, minimum-scale=1, maximum-scale=1, user-scalable=no",t.parentNode!==e.head&&(t.name="viewport",e.head.appendChild(t));var r=N.dataset;y={width:n.util.toNumber(r.width,h),height:n.util.toNumber(r.height,b),maxScale:n.util.toNumber(r.maxScale,E),minScale:n.util.toNumber(r.minScale,L),perspective:n.util.toNumber(r.perspective,x),transitionDuration:n.util.toNumber(r.transitionDuration,w)},C=p(y),n.util.arrayify(N.childNodes).forEach((function(e){T.appendChild(e)})),N.appendChild(T),e.documentElement.style.height="100%",l(v,{height:"100%",overflow:"hidden"});var s={position:"absolute",transformOrigin:"top left",transition:"all 0s ease-in-out",transformStyle:"preserve-3d"};l(N,s),l(N,{top:"50%",left:"50%",perspective:y.perspective/C+"px",transform:c(C)}),l(T,s),v.classList.remove("impress-disabled"),v.classList.add("impress-enabled"),B(),o={translate:{x:0,y:0,z:0},rotate:{x:0,y:0,z:0,order:"xyz"},scale:1},z=!0,n.util.triggerEvent(N,"impress:init",{api:f["impress-root-"+i]})}},goto:H,next:function(e){var t=g.indexOf(s)+1;return t=t<g.length?g[t]:g[0],H(t,void 0,"next",e)},prev:function(e){var t=g.indexOf(s)-1;return t=t>=0?g[t]:g[g.length-1],H(t,void 0,"prev",e)},swipe:function(e){if(!(Math.abs(e)>1)){var t,n={target:s,detail:{}};if(n.detail.swipe=e,n.detail.transitionDuration=y.transitionDuration,e<0)t=g.indexOf(s)+1,n.detail.next=t<g.length?g[t]:g[0],n.detail.reason="next";else{if(!(e>0))return;t=g.indexOf(s)-1,n.detail.next=t>=0?g[t]:g[g.length-1],n.detail.reason="prev"}if(!1===A(n))return!1;var i=n.detail.next,a=r["impress-"+i.id],p=a.scale*C,v=Math.abs(e),m={translate:{x:q(o.translate.x,-a.translate.x,v),y:q(o.translate.y,-a.translate.y,v),z:q(o.translate.z,-a.translate.z,v)},rotate:{x:q(o.rotate.x,-a.rotate.x,v),y:q(o.rotate.y,-a.rotate.y,v),z:q(o.rotate.z,-a.rotate.z,v),order:v<.7?o.rotate.order:a.rotate.order},scale:q(o.scale*C,p,v)};l(N,{perspective:y.perspective/m.scale+"px",transform:c(m.scale),transitionDuration:"0ms",transitionDelay:"0ms"}),l(T,{transform:u(m.rotate,!0)+d(m.translate),transitionDuration:"0ms",transitionDelay:"0ms"})}},tear:function(){n.gc.teardown(),delete f["impress-root-"+i]},lib:n}};C.supported=m;var N={};C.addLibraryFactory=function(e){for(var t in e)e.hasOwnProperty(t)&&(N[t]=e[t])};var S=function(e){var t={};for(var n in N)if(N.hasOwnProperty(n)){if(void 0!==t[n])throw"impress.js ERROR: Two libraries both tried to use libname: "+n;t[n]=N[n](e)}return t};C.addPreInitPlugin=function(e,t){if((t=parseInt(t)||10)<=0)throw"addPreInitPlugin: weight must be a positive integer";void 0===g[t]&&(g[t]=[]),g[t].push(e)};var D=function(e){for(var t=0;t<g.length;t++){var n=g[t];if(void 0!==n)for(var i=0;i<n.length;i++)n[i](e)}};C.addPreStepLeavePlugin=function(e,t){if((t=parseInt(t)||10)<=0)throw"addPreStepLeavePlugin: weight must be a positive integer";void 0===y[t]&&(y[t]=[]),y[t].push(e)};var A=function(e){for(var t=0;t<y.length;t++){var n=y[t];if(void 0!==n)for(var i=0;i<n.length;i++)if(!1===n[i](e))return!1}}}(document,window),function(e,t){"use strict";var n=[],i=0,r={roots:[]};t.impress.addLibraryFactory({gc:function(e){if(n[e])return n[e];var t=[],r=[],a=[];s(e);var l=function(e){t.push(e)},d=function(e,t,n){r.push({target:e,type:t,listener:n})},u=function(e){a.push(e)};u((function(e){o(e)}));var c={pushElement:l,appendChild:function(e,t){e.appendChild(t),l(t)},pushEventListener:d,addEventListener:function(e,t,n){e.addEventListener(t,n),d(e,t,n)},pushCallback:u,teardown:function(){var n;for(n=a.length-1;n>=0;n--)a[n](e);for(a=[],n=0;n<t.length;n++)t[n].parentElement.removeChild(t[n]);for(t=[],n=0;n<r.length;n++){var i=r[n].target,s=r[n].type,o=r[n].listener;i.removeEventListener(s,o)}}};return n[e]=c,i++,c}});var s=function(t){r.roots[t]={},r.roots[t].steps=[];for(var n=e.getElementById(t).querySelectorAll(".step"),s=0;s<n.length;s++){var o=n[s];r.roots[t].steps.push({el:o,id:o.getAttribute("id")})}if(0===i){r.body={},e.body.classList.contains("impress-not-supported")?r.body.impressNotSupported=!0:r.body.impressNotSupported=!1;var a=e.head.querySelectorAll("meta");for(s=0;s<a.length;s++){var l=a[s];"viewport"===l.name&&(r.meta=l.content)}}},o=function(t){e.body.classList.remove("impress-enabled"),e.body.classList.remove("impress-disabled");var s=e.getElementById(t),o=s.querySelector(".active").id;e.body.classList.remove("impress-on-"+o),e.documentElement.style.height="",e.body.style.height="",e.body.style.overflow="";for(var a,l=s.querySelectorAll(".step"),d=0;d<l.length;d++)l[d].classList.remove("future"),l[d].classList.remove("past"),l[d].classList.remove("present"),l[d].classList.remove("active"),l[d].style.position="",l[d].style.transform="",l[d].style["transform-style"]="";for(s.style.position="",s.style["transform-origin"]="",s.style.transition="",s.style["transform-style"]="",s.style.top="",s.style.left="",s.style.transform="",l=r.roots[t].steps;a=l.pop();)null===a.id?a.el.removeAttribute("id"):a.el.setAttribute("id",a.id);delete r.roots[t];var u=s.firstChild.innerHTML;if(s.innerHTML=u,void 0!==n[t]&&(delete n[t],i--),0===i){e.body.classList.remove("impress-supported"),r.body.impressNotSupported&&e.body.classList.add("impress-not-supported");var c=e.head.querySelectorAll("meta");for(d=0;d<c.length;d++){var p=c[d];"viewport"===p.name&&(void 0!==r.meta?p.content=r.meta:p.parentElement.removeChild(p))}}}}(document,window),function(e,t){"use strict";var n=[];t.impress.addLibraryFactory({util:function(i){if(n[i])return n[i];var r=function(e){return[].slice.call(e)},s=function(t){return e.getElementById(t)},o={$:function(t,n){return(n=n||e).querySelector(t)},$$:function(t,n){return r((n=n||e).querySelectorAll(t))},arrayify:r,byId:s,getElementFromHash:function(){return s(t.location.hash.replace(/^#\/?/,""))},throttle:function(e,n){var i=null;return function(){var r=this,s=arguments;t.clearTimeout(i),i=t.setTimeout((function(){e.apply(r,s)}),n)}},toNumber:function(e,t){return isNaN(e)?t||0:Number(e)},triggerEvent:function(t,n,i){var r=e.createEvent("CustomEvent");r.initCustomEvent(n,!0,!0,i),t.dispatchEvent(r)},getUrlParamValue:function(e){var n=t.location.search.split(e+"=")[1],i=n&&n.split("&")[0];if(""!==i)return i}};return n[i]=o,o}})}(document,window),function(e){"use strict";var t,n=0,i=0,r=null,s=null;e.addEventListener("impress:init",(function(i){t=i.detail.api.lib.util,r=i.detail.api;var o=i.target.dataset,a=t.getUrlParamValue("impress-autoplay")||o.autoplay;a&&(n=t.toNumber(a,0));var l=e.querySelector("#impress-toolbar");l&&p(l),r.lib.gc.pushCallback((function(){clearTimeout(s)}))}),!1),e.addEventListener("impress:autoplay:pause",(function(e){l="paused",o(e)}),!1),e.addEventListener("impress:autoplay:play",(function(e){l="playing",o(e)}),!1);var o=function(e){var r=e.target;i=t.toNumber(r.dataset.autoplay,n),a("paused"===l?0:i)};e.addEventListener("impress:stepenter",(function(e){o(e)}),!1),e.addEventListener("impress:substep:enter",(function(e){o(e)}),!1);var a=function(e){s&&clearTimeout(s),e>0&&(s=setTimeout((function(){r.next()}),1e3*e)),c()},l="not clicked",d=null,u=function(){return i>0&&"paused"!==l?"||":"&#9654;"},c=function(){if(d){var e=d.offsetWidth,t=d.offsetHeight;d.innerHTML=u(),d.style.width||(d.style.width=e+"px"),d.style.height||(d.style.height=t+"px")}},p=function(r){var s='<button id="impress-autoplay-playpause" title="Autoplay" class="impress-autoplay">'+u()+"</button>";(d=function(t){var n=e.createElement("div");return n.innerHTML=t,n.firstChild}(s)).addEventListener("click",(function(){"playing"===(l=i>0&&"paused"!==l?"paused":"playing")?(0===n&&(n=7),0===i&&(i=n),a(i)):"paused"===l&&a(0)})),t.triggerEvent(r,"impress:toolbar:appendChild",{group:10,element:d})}}(document),function(e){"use strict";var t,n,i,r=null,s=!1,o=null,a=null,l=function(e,t){var n,i;for(n in t)t.hasOwnProperty(n)&&null!==(i=d(n))&&(e.style[i]=t[n]);return e},d=(t=e.createElement("dummy").style,n="Webkit Moz O ms Khtml".split(" "),i={},function(e){if(void 0===i[e]){var r=e.charAt(0).toUpperCase()+e.substr(1),s=(e+" "+n.join(r+" ")+r).split(" ");for(var o in i[e]=null,s)if(void 0!==t[s[o]]){i[e]=s[o];break}}return i[e]}),u=function(){s&&(l(r,{display:"block"}),s=!1,null.triggerEvent(o,"impress:autoplay:play",{}))},c=function(){s?u():(l(r,{display:(s=!s)?"none":"block"}),s=!0,null.triggerEvent(o,"impress:autoplay:pause",{}))};e.addEventListener("impress:init",(function(t){(a=t.detail.api).lib.util,o=t.target,r=o.firstElementChild;var n=a.lib.gc;a.lib.util;n.addEventListener(e,"keydown",(function(e){66!==e.keyCode&&190!==e.keyCode||(e.preventDefault(),s?u():c())}),!1),n.addEventListener(e,"keyup",(function(e){66!==e.keyCode&&190!==e.keyCode||e.preventDefault()}),!1)}),!1),e.addEventListener("impress:stepleave",(function(){u()}),!1)}(document),function(e,t){"use strict";impress.addPreInitPlugin((function(){if(t.markdown)for(var n=e.querySelectorAll(".markdown"),i=0;i<n.length;i++){var r=n[i],s=r.dataset.markdownDialect,o=r.textContent.split(/^-----$/m),a=o.length-1;r.innerHTML=markdown.toHTML(o[a],s);var l=null;for(r.id&&(l=r.id,r.id=""),a--;a>=0;){var d=r.cloneNode(!1);d.innerHTML=markdown.toHTML(o[a]),r.parentNode.insertBefore(d,r),r=d,a--}null!==l&&(r.id=l)}t.hljs&&hljs.initHighlightingOnLoad(),t.mermaid&&mermaid.initialize({startOnLoad:!0})}),1)}(document,window),function(e){"use strict";e.addEventListener("impress:init",(function(t){t.target;var n=t.detail.api.lib.gc;for(var i of["input","textarea","select","[contenteditable=true]"]){var r=e.querySelectorAll(i);if(r)for(var s=0;s<r.length;s++){var o=r[s];n.addEventListener(o,"keydown",(function(e){e.stopPropagation()})),n.addEventListener(o,"keyup",(function(e){e.stopPropagation()}))}}}),!1),e.addEventListener("impress:stepleave",(function(){e.activeElement.blur()}),!1)}(document),function(e){"use strict";e.addEventListener("impress:init",(function(t){var n=t.detail.api,i=t.target,r=n.lib.gc,s=n.lib.util;r.addEventListener(e,"keydown",(function(t){var n;"F5"===t.code&&(t.preventDefault(),n=e.documentElement,e.fullscreenElement||n.requestFullscreen(),s.triggerEvent(i.querySelector(".active"),"impress:steprefresh")),"Escape"!==t.key&&"F5"!==t.key||(t.preventDefault(),e.fullscreenElement&&e.exitFullscreen(),s.triggerEvent(i.querySelector(".active"),"impress:steprefresh"))}),!1),s.triggerEvent(e,"impress:help:add",{command:"F5 / ESC",text:"Fullscreen: Enter / Exit",row:200})}),!1)}(document),function(e,t){"use strict";var n;e.addEventListener("impress:init",(function(e){n=e.detail.api.lib}),!1);var i=function(e){return!isNaN(e)};impress.addPreStepLeavePlugin((function(r){if(r&&r.target){var s=r.target.dataset,o=e.querySelectorAll(".step");if(void 0!==s.gotoKeyList&&void 0!==s.gotoNextList&&void 0!==r.origEvent&&void 0!==r.origEvent.key){var a=s.gotoKeyList.split(" "),l=s.gotoNextList.split(" ");if(a.length!==l.length)t.console.log("impress goto plugin: data-goto-key-list and data-goto-next-list don't match:"),t.console.log(a),t.console.log(l);else{var d=a.indexOf(r.origEvent.key);if(d>=0){var u=l[d];if(i(u))return r.detail.next=o[u],void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));if((c=e.getElementById(u))&&c.classList.contains("step"))return r.detail.next=c,void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));t.console.log("impress goto plugin: "+u+" is not a step in this impress presentation.")}}}if(i(s.gotoNext)&&"next"===r.detail.reason)return r.detail.next=o[s.gotoNext],void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));if(s.gotoNext&&"next"===r.detail.reason){if((c=e.getElementById(s.gotoNext))&&c.classList.contains("step"))return r.detail.next=c,void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));t.console.log("impress goto plugin: "+s.gotoNext+" is not a step in this impress presentation.")}if(i(s.gotoPrev)&&"prev"===r.detail.reason)return r.detail.next=o[s.gotoPrev],void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));if(s.gotoPrev&&"prev"===r.detail.reason){if((c=e.getElementById(s.gotoPrev))&&c.classList.contains("step"))return r.detail.next=c,void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));t.console.log("impress goto plugin: "+s.gotoPrev+" is not a step in this impress presentation.")}if(i(s.goto))return r.detail.next=o[s.goto],void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));if(s.goto){var c;if((c=e.getElementById(s.goto))&&c.classList.contains("step"))return r.detail.next=c,void(r.detail.transitionDuration=n.util.toNumber(r.detail.next.dataset.transitionDuration,r.detail.transitionDuration));t.console.log("impress goto plugin: "+s.goto+" is not a step in this impress presentation.")}}}))}(document,window),function(e,t){"use strict";var n,i=[],r=function(){var t=e.getElementById("impress-help");if(t){var n=[];for(var r in i)for(var s in r)n.push(i[r][s]);n&&(t.innerHTML="<table>\n"+n.join("\n")+"</table>\n")}};e.addEventListener("keyup",(function(i){var r;72!==i.keyCode&&191!==i.keyCode||(i.preventDefault(),(r=e.getElementById("impress-help"))&&("block"===r.style.display?r.style.display="none":(r.style.display="block",t.clearTimeout(n))))}),!1),e.addEventListener("impress:help:add",(function(e){var t=e.detail.row;"object"==typeof i[t]&&i[t].isArray||(i[t]=[]),i[e.detail.row].push("<tr><td><strong>"+e.detail.command+"</strong></td><td>"+e.detail.text+"</td></tr>"),r()})),e.addEventListener("impress:init",(function(s){r();var o,a,l,d,u=e.getElementById("impress-help");u&&(u.style.display="block",n=t.setTimeout((function(){e.getElementById("impress-help").style.display="none"}),7e3),s.detail.api.lib.gc.pushCallback((function(){t.clearTimeout(n),u.style.display="",u.innerHTML="",i=[]})));o=e,a="impress:help:add",l={command:"H",text:"Show this help",row:0},(d=e.createEvent("CustomEvent")).initCustomEvent(a,!0,!0,l),o.dispatchEvent(d)}))}(document,window),function(e,t){"use strict";var n;switch(navigator.language){case"de":n={noNotes:'<div class="noNotes">Keine Notizen hierzu</div>',restart:"Neustart",clickToOpen:"Klicken um Sprecherkonsole zu öffnen",prev:"zurück",next:"weiter",loading:"initalisiere",ready:"Bereit",moving:"in Bewegung",useAMPM:!1};break;case"en":default:n={noNotes:'<div class="noNotes">No notes for this step</div>',restart:"Restart",clickToOpen:"Click to open speaker console",prev:"Prev",next:"Next",loading:"Loading",ready:"Ready",moving:"Moving",useAMPM:!1}}const i='<!DOCTYPE html><html id="impressconsole"><head>{{cssStyle}}{{cssLink}}</head><body><div id="console"><div id="views"><iframe id="slideView" scrolling="no"></iframe><iframe id="preView" scrolling="no"></iframe><div id="blocker"></div></div><div id="notes"></div></div><div id="controls"> <div id="prev"><a  href="#" onclick="impress().prev(); return false;" />{{prev}}</a></div><div id="next"><a  href="#" onclick="impress().next(); return false;" />{{next}}</a></div><div id="clock">--:--</div><div id="timer" onclick="timerReset()">00m 00s</div><div id="status">{{loading}}</div></div></body></html>';var r=void 0,s=void 0,o={},a=function(e){return(e<10?"0":"")+e},l=t.impressConsole=function(l){if(o[l=l||"impress"])return o[l];var u=e.getElementById(l),c=null,p=function(){for(var t="",i=e.querySelector(".active");!i.nextElementSibling&&i.parentNode;)i=i.parentNode;for(i=i.nextElementSibling;i;){if((t=i.attributes.class)&&-1!==t.value.indexOf("step"))return c.document.getElementById("blocker").innerHTML=n.next,i;if(i.firstElementChild)i=i.firstElementChild;else{for(;!i.nextElementSibling&&i.parentNode;)i=i.parentNode;i=i.nextElementSibling}}return c.document.getElementById("blocker").innerHTML=n.restart,e.querySelector(".step")},v=function(){if(c){var t=e.querySelector(".active").querySelector(".notes");t=t?t.innerHTML:n.noNotes,c.document.getElementById("notes").innerHTML=t;var i=e.URL.substring(0,e.URL.search("#/")),r=i+"#"+e.querySelector(".active").id,s=i+"#"+p().id,o=c.document.getElementById("slideView");o.src!==r&&(o.src=r);var a=c.document.getElementById("preView");a.src!==s&&(a.src=s),c.document.getElementById("status").innerHTML='<span class="moving">'+n.moving+"</span>"}},m=function(){if(c){var t=e.querySelector(".active").querySelector(".notes");t=t?t.innerHTML:n.noNotes;var i=c.document.getElementById("notes");i.innerHTML=t,i.scrollTop=0;var r=e.URL.substring(0,e.URL.search("#/")),s=r+"#"+e.querySelector(".active").id,o=r+"#"+p().id,a=c.document.getElementById("slideView");a.src!==s&&(a.src=s);var l=c.document.getElementById("preView");l.src!==o&&(l.src=o),c.document.getElementById("status").innerHTML='<span  class="ready">'+n.ready+"</span>"}},f=function(e){c&&("next"===e.detail.reason&&g(),"prev"===e.detail.reason&&y())},g=function(){var e=c.document.getElementById("slideView");h(e,"impress:substep:show")},y=function(){var e=c.document.getElementById("slideView");h(e,"impress:substep:hide")},h=function(e,t,n){var i=e.contentDocument.createEvent("CustomEvent");i.initCustomEvent(t,!0,!0,n),e.contentDocument.dispatchEvent(i)},b=function(){var e=c.document.getElementById("notes");e.scrollTopMax-e.scrollTop>20?e.scrollTop=e.scrollTop+.8*e.clientHeight:t.impress().next()},E=function(){c.timerStart=new Date},L=function(e,t,n){void 0===n&&(n=c),n.document.addEventListener("keydown",(function(t){t.ctrlKey||t.altKey||t.shiftKey||t.metaKey||-1===e.indexOf(t.keyCode)||t.preventDefault()}),!1),n.document.addEventListener("keyup",(function(n){n.ctrlKey||n.altKey||n.shiftKey||n.metaKey||-1===e.indexOf(n.keyCode)||(t(),n.preventDefault())}),!1)},x=function(){var e=c.document.getElementById("slideView"),t=c.document.getElementById("preView");e.contentDocument.body.classList.add("impress-console"),t.contentDocument.body.classList.add("impress-console"),void 0!==s&&(e.contentDocument.head.insertAdjacentHTML("beforeend",'<link rel="stylesheet" type="text/css" href="'+s+'">'),t.contentDocument.head.insertAdjacentHTML("beforeend",'<link rel="stylesheet" type="text/css" href="'+s+'">')),e.addEventListener("load",(function(){e.contentDocument.body.classList.add("impress-console"),void 0!==s&&e.contentDocument.head.insertAdjacentHTML("beforeend",'<link rel="stylesheet" type="text/css" href="'+s+'">')})),t.addEventListener("load",(function(){t.contentDocument.body.classList.add("impress-console"),void 0!==s&&t.contentDocument.head.insertAdjacentHTML("beforeend",'<link rel="stylesheet" type="text/css" href="'+s+'">')}))},w=function(){if(!top.isconsoleWindow){if(!c||c.closed){if(null==(c=t.open("","impressConsole"))){var s=e.createElement("div");s.id="impress-console-button",s.style.position="fixed",s.style.left=0,s.style.top=0,s.style.right=0,s.style.bottom=0,s.style.backgroundColor="rgba(255, 255, 255, 0.9)";var a="var x = document.getElementById('impress-console-button');x.parentNode.removeChild(x);var r = document.getElementById('"+l+"');impress('"+l+"').lib.util.triggerEvent(r, 'impress:console:open', {})";return s.innerHTML='<button style="margin: 25vh 25vw;width:50vw;height:50vh;" onclick="'+a+'">'+n.clickToOpen+"</button>",void e.body.appendChild(s)}var u="";return void 0!==r&&(u='<link rel="stylesheet" type="text/css" media="screen" href="'+r+'">'),c.document.open(),c.document.write(i.replace("{{cssStyle}}",d()).replace("{{cssLink}}",u).replace(/{{.*?}}/gi,(function(e){return n[e.substring(2,e.length-2)]}))),c.document.title="Speaker Console ("+e.title+")",c.impress=t.impress,c.isconsoleWindow=!0,c.onload=x,c.timerStart=new Date,c.timerReset=E,c.clockInterval=setInterval(o[l].clockTick,1e3),L([33,37,38],t.impress().prev),L([34,39,40],t.impress().next),L([32],b),L([82],E),c.onbeforeunload=function(){clearInterval(c.clockInterval)},m(),c.initialized=!1,c.document.close(),t.onresize=k,c.onresize=k,c}c.focus()}},k=function(){var e=c.document.getElementById("slideView"),n=c.document.getElementById("preView"),i=t.innerHeight/t.innerWidth,r=c.document.getElementById("views"),s=e.offsetWidth-e.clientWidth,o=r.clientWidth-s,a=Math.floor(o*i),l=a+4,d=Math.floor(.7*o),u=Math.floor(.7*a);r.clientHeight-s<l+u&&(u=r.clientHeight-s-l,d=Math.floor(u/i)),d<=Math.floor(.5*o)&&(o=r.clientWidth-s,l=(a=Math.floor((r.clientHeight-s-4)/1.5))+4,d=Math.floor(.5*o),u=r.clientHeight-s-l),e.style.width=o+"px",e.style.height=a+"px",n.style.top=l+"px",n.style.width=d+"px",n.style.height=u+"px"},C=function(e,n){void 0!==e?r=e:void 0!==u.dataset.consoleCss&&(r=u.dataset.consoleCss),void 0!==n?s=n:void 0!==u.dataset.consoleCssIframe&&(s=u.dataset.consoleCssIframe),u.addEventListener("impress:stepleave",v),u.addEventListener("impress:stepenter",m),u.addEventListener("impress:substep:stepleaveaborted",f),u.addEventListener("impress:substep:show",g),u.addEventListener("impress:substep:hide",y),t.onunload=function(){c&&!c.closed&&c.close()},L([80],w,t),"true"===u.dataset.consoleAutolaunch&&w()};return u.addEventListener("impress:console:open",(function(){w()})),u.addEventListener("impress:console:registerKeyEvent",(function(e){L(e.detail.keyCodes,e.detail.handler,e.detail.window)})),o[l]={init:function(e,n){void 0!==e&&"css/impressConsole.css"!==e||void 0!==n&&"css/iframe.css"!==n||t.console.log("impressConsole().init() is deprecated. impressConsole is now initialized automatically when you call impress().init()."),C(e,n)},open:w,clockTick:function(){var e=new Date,t=e.getHours(),i=e.getMinutes(),r=e.getSeconds(),s="";n.useAMPM&&(s=t<12?"AM":"PM",t=0===(t=t>12?t-12:t)?12:t);var o=a(t)+":"+a(i)+":"+a(r)+" "+s;c.document.getElementById("clock").firstChild.nodeValue=o,r=Math.floor((e-c.timerStart)/1e3),i=Math.floor(r/60),r=Math.floor(r%60),c.document.getElementById("timer").firstChild.nodeValue=a(i)+"m "+a(r)+"s",c.initialized||(c.document.getElementById("slideView").contentWindow.scrollTo(0,0),c.document.getElementById("preView").contentWindow.scrollTo(0,0),c.initialized=!0)},registerKeyEvent:L,_init:C},o[l]};e.addEventListener("impress:init",(function(t){l(t.target.id)._init(),function(t,n,i){var r=e.createEvent("CustomEvent");r.initCustomEvent(n,!0,!0,i),t.dispatchEvent(r)}(e,"impress:help:add",{command:"P",text:"Presenter console",row:10})}));var d=function(){return"<style>\n            #impressconsole body {\n                background-color: rgb(255, 255, 255);\n                padding: 0;\n                margin: 0;\n                font-family: verdana, arial, sans-serif;\n                font-size: 2vw;\n            }\n\n            #impressconsole div#console {\n                position: absolute;\n                top: 0.5vw;\n                left: 0.5vw;\n                right: 0.5vw;\n                bottom: 3vw;\n                margin: 0;\n            }\n\n            #impressconsole div#views, #impressconsole div#notes {\n                position: absolute;\n                top: 0;\n                bottom: 0;\n            }\n\n            #impressconsole div#views {\n                left: 0;\n                right: 50vw;\n                overflow: hidden;\n            }\n\n            #impressconsole div#blocker {\n                position: absolute;\n                right: 0;\n                bottom: 0;\n            }\n\n            #impressconsole div#notes {\n                left: 50vw;\n                right: 0;\n                overflow-x: hidden;\n                overflow-y: auto;\n                padding: 0.3ex;\n                background-color: rgb(255, 255, 255);\n                border: solid 1px rgb(120, 120, 120);\n            }\n\n            #impressconsole div#notes .noNotes {\n                color: rgb(200, 200, 200);\n            }\n\n            #impressconsole div#notes p {\n                margin-top: 0;\n            }\n\n            #impressconsole iframe {\n                position: absolute;\n                margin: 0;\n                padding: 0;\n                left: 0;\n                border: solid 1px rgb(120, 120, 120);\n            }\n\n            #impressconsole iframe#slideView {\n                top: 0;\n                width: 49vw;\n                height: 49vh;\n            }\n\n            #impressconsole iframe#preView {\n                opacity: 0.7;\n                top: 50vh;\n                width: 30vw;\n                height: 30vh;\n            }\n\n            #impressconsole div#controls {\n                margin: 0;\n                position: absolute;\n                bottom: 0.25vw;\n                left: 0.5vw;\n                right: 0.5vw;\n                height: 2.5vw;\n                background-color: rgb(255, 255, 255);\n                background-color: rgba(255, 255, 255, 0.6);\n            }\n\n            #impressconsole div#prev, div#next {\n            }\n\n            #impressconsole div#prev a, #impressconsole div#next a {\n                display: block;\n                border: solid 1px rgb(70, 70, 70);\n                border-radius: 0.5vw;\n                font-size: 1.5vw;\n                padding: 0.25vw;\n                text-decoration: none;\n                background-color: rgb(220, 220, 220);\n                color: rgb(0, 0, 0);\n            }\n\n            #impressconsole div#prev a:hover, #impressconsole div#next a:hover {\n                background-color: rgb(245, 245, 245);\n            }\n\n            #impressconsole div#prev {\n                float: left;\n            }\n\n            #impressconsole div#next {\n                float: right;\n            }\n\n            #impressconsole div#status {\n                margin-left: 2em;\n                margin-right: 2em;\n                text-align: center;\n                float: right;\n            }\n\n            #impressconsole div#clock {\n                margin-left: 2em;\n                margin-right: 2em;\n                text-align: center;\n                float: left;\n            }\n\n            #impressconsole div#timer {\n                margin-left: 2em;\n                margin-right: 2em;\n                text-align: center;\n                float: left;\n            }\n\n            #impressconsole span.moving {\n                color: rgb(255, 0, 0);\n            }\n\n            #impressconsole span.ready {\n                color: rgb(0, 128, 0);\n            }\n        </style>"}}(document,window),function(e,t){"use strict";var n,i,r,s,o,a,l,d,u,c,p,v,m,f,g;s=[],e.addEventListener("impress:init",(function(e){n=e.target,i=e.detail.api,r=i.lib.gc,a(),r.pushCallback(g)}),!1),g=function(){var e,t;for(l(),t=0;t<s.length;t+=1)(e=s[t]).node.removeAttribute(e.attr);s=[]},f=function(e,t){var n,i,r,s;for(n="data-media-"+e,r=0;r<t.length;r+=1)if((s=t[r]).hasAttribute(n))return""===(i=s.getAttribute(n))||"true"===i},p=function(t){var n=t.target.nodeName.toLowerCase();e.body.classList.add("impress-media-"+n+"-playing"),e.body.classList.remove("impress-media-"+n+"-paused")},v=function(t){var n=t.target.nodeName.toLowerCase();e.body.classList.add("impress-media-"+n+"-paused"),e.body.classList.remove("impress-media-"+n+"-playing")},m=function(t){var n=t.target.nodeName.toLowerCase();e.body.classList.remove("impress-media-"+n+"-playing"),e.body.classList.remove("impress-media-"+n+"-paused")},l=function(){var t,n;for(t in n=["video","audio"])e.body.classList.remove("impress-media-"+n[t]+"-playing"),e.body.classList.remove("impress-media-"+n[t]+"-paused")},o=function(){var e,t,i,o;for(t=n.querySelectorAll("audio, video"),e=0;e<t.length;e+=1)o=t[e].nodeName.toLowerCase(),null==(i=t[e]).getAttribute("id")&&(i.setAttribute("id","media-"+o+"-"+e),s.push({node:i,attr:"id"})),r.addEventListener(i,"play",p),r.addEventListener(i,"playing",p),r.addEventListener(i,"pause",v),r.addEventListener(i,"ended",m)},a=function(){var t,n,i;for(o(),t=e.getElementsByClassName("step"),i=0;i<t.length;i+=1)n=t[i],r.addEventListener(n,"impress:stepenter",u),r.addEventListener(n,"impress:stepleave",c)},d=function(){return{preview:null!==t.frameElement&&"preView"===t.frameElement.id,slideView:null!==t.frameElement&&"slideView"===t.frameElement.id}},u=function(e){var t,i,r,s,o;if(e&&e.target)for(t=e.target,l(),i=t.querySelectorAll("audio, video"),s=0;s<i.length;s+=1)r=i[s],o=d(),f("autoplay",[r,t,n])&&!o.preview&&(o.slideView&&(r.muted=!0),r.play())},c=function(e){var t,i,r,s,o,a,d;if(e&&e.target){for(t=e.target,i=e.target.querySelectorAll("audio, video"),r=0;r<i.length;r+=1)s=i[r],o=f("autoplay",[s,t,n]),a=f("autopause",[s,t,n]),void 0===(d=f("autostop",[s,t,n]))&&void 0===a&&(d=o),(a||d)&&(s.pause(),d&&(s.currentTime=0));l()}}}(document,window),function(e){"use strict";e.addEventListener("impress:init",(function(t){var n=e.body;/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)&&n.classList.add("impress-mobile"),t.detail.api.lib.gc.pushCallback((function(){e.body.classList.remove("impress-mobile");var t=e.getElementsByClassName("prev")[0],n=e.getElementsByClassName("next")[0];void 0!==t&&t.classList.remove("prev"),void 0!==n&&n.classList.remove("next")}))})),e.addEventListener("impress:stepenter",(function(t){var n=e.getElementsByClassName("prev")[0],i=e.getElementsByClassName("next")[0];(function(t){for(var n=e.querySelectorAll(".step"),i=n.length-1;i>=0;i--)if(n[i]===t)return i-1>=0?n[i-1]:n[n.length-1]})(t.target).classList.add("prev"),function(t){for(var n=e.querySelectorAll(".step"),i=0;i<n.length;i++)if(n[i]===t)return i+1<n.length?n[i+1]:n[0]}(t.target).classList.add("next"),void 0!==n&&n.classList.remove("prev"),void 0!==i&&i.classList.remove("next")}))}(document),function(e,t){"use strict";var n,i=function(){e.body.classList.add("impress-mouse-timeout")},r=function(){n&&t.clearTimeout(n),e.body.classList.remove("impress-mouse-timeout"),n=t.setTimeout(i,3e3)};e.addEventListener("impress:init",(function(i){var s=i.detail.api.lib.gc;s.addEventListener(e,"mousemove",r),s.addEventListener(e,"click",r),s.addEventListener(e,"touch",r),r(),s.pushCallback((function(){t.clearTimeout(n),e.body.classList.remove("impress-mouse-timeout")}))}),!1)}(document,window),function(e){"use strict";e.addEventListener("impress:init",(function(t){var n=t.detail.api,i=n.lib.gc,r=n.lib.util,s=function(e){return!(e.altKey||e.ctrlKey||e.metaKey)&&(9===e.keyCode||!e.shiftKey&&(e.keyCode>=32&&e.keyCode<=34||e.keyCode>=37&&e.keyCode<=40||void 0))};i.addEventListener(e,"keydown",(function(e){s(e)&&e.preventDefault()}),!1),i.addEventListener(e,"keyup",(function(e){if(s(e)){if(e.shiftKey)switch(e.keyCode){case 9:n.prev()}else switch(e.keyCode){case 33:case 37:case 38:n.prev(e);break;case 9:case 32:case 34:case 39:case 40:n.next(e)}e.preventDefault()}}),!1),i.addEventListener(e,"click",(function(t){var i=t.target;try{for(;"A"!==i.tagName&&i!==e.documentElement;)i=i.parentNode;if("A"===i.tagName){var r=i.getAttribute("href");r&&"#"===r[0]&&(i=e.getElementById(r.slice(1)))}n.goto(i)&&(t.stopImmediatePropagation(),t.preventDefault())}catch(e){if(e instanceof TypeError&&"target is null"===e.message)return;throw e}}),!1),i.addEventListener(e,"click",(function(t){var i=t.target;try{for(;(!i.classList.contains("step")||i.classList.contains("active"))&&i!==e.documentElement;)i=i.parentNode;n.goto(i)&&t.preventDefault()}catch(e){if(e instanceof TypeError&&"target is null"===e.message)return;throw e}}),!1),r.triggerEvent(e,"impress:help:add",{command:"Left &amp; Right",text:"Previous &amp; Next step",row:1})}),!1)}(document),function(e){"use strict";var t,n,i,r,s,o,a,l=[],d=function(t,n,i){var r=e.createEvent("CustomEvent");r.initCustomEvent(n,!0,!0,i),t.dispatchEvent(r)},u=function(t){var n=e.createElement("div");return n.innerHTML=t,n.firstChild},c=function(){for(var e="",t=0;t<r.length;t++)l.indexOf(r[t])<0&&(e=e+'<option value="'+r[t].id+'">'+r[t].id+"</option>\n");return e};e.addEventListener("impress:navigation-ui:hideStep",(function(e){l.push(e.target),o&&(o.innerHTML=c())}),!1),e.addEventListener("impress:init",(function(l){(t=e.querySelector("#impress-toolbar"))&&function(e){var l=(n=e.detail.api).lib.gc;i=e.target,r=i.querySelectorAll(".step");var p='<select id="impress-navigation-ui-select" title="Go to" class="impress-navigation-ui">\n'+c()+"</select>";(s=u('<button id="impress-navigation-ui-prev" title="Previous" class="impress-navigation-ui">&lt;</button>')).addEventListener("click",(function(){n.prev()})),(o=u(p)).addEventListener("change",(function(e){n.goto(e.target.value)})),l.addEventListener(i,"impress:steprefresh",(function(e){r=i.querySelectorAll(".step"),o.innerHTML="\n"+c(),o.value=e.target.id})),(a=u('<button id="impress-navigation-ui-next" title="Next" class="impress-navigation-ui">&gt;</button>')).addEventListener("click",(function(){n.next()})),d(t,"impress:toolbar:appendChild",{group:0,element:s}),d(t,"impress:toolbar:appendChild",{group:0,element:o}),d(t,"impress:toolbar:appendChild",{group:0,element:a})}(l)}),!1)}(document),function(e){"use strict";var t,n=[],i=function(){n=[];for(var e=t.querySelectorAll(".step"),i=0;i<e.length;i++)n[i+1]=e[i].id};e.addEventListener("impress:init",(function(e){t=e.target,i(),e.detail.api.lib.gc.pushCallback((function(){n=[],r&&(r.style.width=""),s&&(s.innerHTML="")}))}));var r=e.querySelector("div.impress-progressbar div"),s=e.querySelector("div.impress-progress");function o(e){var t=n.indexOf(e);if(null!==r){var i=100/(n.length-1)*t;r.style.width=i.toFixed(2)+"%"}null!==s&&(s.innerHTML=t+"/"+(n.length-1))}null===r&&null===s||(e.addEventListener("impress:stepleave",(function(e){o(e.detail.next.id)})),e.addEventListener("impress:steprefresh",(function(e){i(),o(e.target.id)})))}(document),function(e,t){"use strict";var n={},i=function(e,t){return isNaN(e)?t||0:Number(e)},r=function(e,n){if("string"!=typeof e)return i(e,n);var r=e.match(/^([+-]*[\d\.]+)([wh])$/);return null==r?i(e,n):parseFloat(r[1])*("w"===r[2]?t.innerWidth:t.innerHeight)},s=function(n,s){var o=n.dataset;if(s||(s={x:0,y:0,z:0,relative:{x:0,y:0,z:0}}),o.relTo){var a=e.getElementById(o.relTo);a?n.compareDocumentPosition(a)&Node.DOCUMENT_POSITION_PRECEDING?(s.x=i(a.getAttribute("data-x")),s.y=i(a.getAttribute("data-y")),s.z=i(a.getAttribute("data-z")),s.relative={}):t.console.error('impress.js rel plugin: Step "'+o.relTo+'" is not defined *before* the current step. Referencing is limited to previously defined steps. Please check your markup. Ignoring data-rel-to attribute of this step. Have a look at the documentation for how to create relative positioning to later shown steps with the help of the goto plugin.'):t.console.warn('impress.js rel plugin: "'+o.relTo+'" is not a valid step in this impress.js presentation. Please check your markup. Ignoring data-rel-to attribute of this step.')}var l={x:i(o.x,s.x),y:i(o.y,s.y),z:i(o.z,s.z),relative:{x:r(o.relX,s.relative.x),y:r(o.relY,s.relative.y),z:r(o.relZ,s.relative.z)}};return void 0!==o.x&&(l.relative.x=0),void 0!==o.y&&(l.relative.y=0),void 0!==o.z&&(l.relative.z=0),l.x=l.x+l.relative.x,l.y=l.y+l.relative.y,l.z=l.z+l.relative.z,l};t.impress.addPreInitPlugin((function(e){var t,i=e.querySelectorAll(".step");n[e.id]=[];for(var r=0;r<i.length;r++){var o=i[r];n[e.id].push({el:o,x:o.getAttribute("data-x"),y:o.getAttribute("data-y"),z:o.getAttribute("data-z"),relX:o.getAttribute("data-rel-x"),relY:o.getAttribute("data-rel-y"),relZ:o.getAttribute("data-rel-z")});var a=s(o,t);o.setAttribute("data-x",a.x),o.setAttribute("data-y",a.y),o.setAttribute("data-z",a.z),t=a}})),e.addEventListener("impress:init",(function(e){var t=e.target;e.detail.api.lib.gc.pushCallback((function(){for(var e,i=n[t.id];e=i.pop();)null!==e.relX&&(null===e.x?e.el.removeAttribute("data-x"):e.el.setAttribute("data-x",e.x)),null!==e.relY&&(null===e.y?e.el.removeAttribute("data-y"):e.el.setAttribute("data-y",e.y)),null!==e.relZ&&(null===e.z?e.el.removeAttribute("data-z"):e.el.setAttribute("data-z",e.z));delete n[t.id]}))}),!1)}(document,window),function(e,t){"use strict";e.addEventListener("impress:init",(function(n){var i=n.detail.api;i.lib.gc.addEventListener(t,"resize",i.lib.util.throttle((function(){i.goto(e.querySelector(".step.active"),500)}),250),!1)}),!1)}(document,window),function(e,t){"use strict";var n;e.addEventListener("impress:init",(function(e){n=e.detail.api.lib.util}),!1);var i=function(t){t&&t.target&&t.detail.next.classList.contains("skip")&&("next"===t.detail.reason?(t.detail.next=function(t){for(var n=e.querySelectorAll(".step"),i=0;i<n.length;i++)if(n[i]===t)return i+1<n.length?n[i+1]:n[0]}(t.detail.next),i(t)):"prev"===t.detail.reason&&(t.detail.next=function(t){for(var n=e.querySelectorAll(".step"),i=n.length-1;i>=0;i--)if(n[i]===t)return i-1>=0?n[i-1]:n[n.length-1]}(t.detail.next),i(t)),t.detail.transitionDuration=n.toNumber(t.detail.next.dataset.transitionDuration,t.detail.transitionDuration))};t.impress.addPreStepLeavePlugin(i,1)}(document,window),function(e,t){"use strict";t.impress.addPreStepLeavePlugin((function(e){if(e&&e.target)return(!e.target.classList.contains("stop")||"next"!==e.detail.reason)&&void 0}),2)}(document,window),function(e,t){"use strict";var n=function(t,n,i){var r=e.createEvent("CustomEvent");r.initCustomEvent(n,!0,!0,i),t.dispatchEvent(r)},i=null;e.addEventListener("impress:stepenter",(function(e){i=e.target}),!1);var r=function(e){var t=e.querySelectorAll(".substep"),n=e.querySelectorAll(".substep-visible");if(t.length>0)return s(t,n)},s=function(e,t){if(t.length<e.length){for(var n=0;n<e.length;n++)e[n].classList.remove("substep-active");var i=e[t.length];return i.classList.add("substep-visible"),i.classList.add("substep-active"),i}},o=function(e){var t=e.querySelectorAll(".substep"),n=e.querySelectorAll(".substep-visible");if(t.length>0)return a(n)},a=function(e){if(e.length>0){for(var t=-1,n=0;n<e.length;n++)e[n].classList.contains("substep-active")&&(t=n),e[n].classList.remove("substep-active");t>0&&e[t-1].classList.add("substep-active");var i=e[e.length-1];return i.classList.remove("substep-visible"),i}};t.impress.addPreStepLeavePlugin((function(e){if(e&&e.target){var t,i=e.target;return"next"===e.detail.reason&&(t=r(i))?(n(i,"impress:substep:stepleaveaborted",{reason:"next",substep:t}),n(i,"impress:substep:enter",{reason:"next",substep:t}),!1):"prev"===e.detail.reason&&(t=o(i))?(n(i,"impress:substep:stepleaveaborted",{reason:"prev",substep:t}),n(i,"impress:substep:leave",{reason:"prev",substep:t}),!1):void 0}}),1),e.addEventListener("impress:stepenter",(function(e){for(var t=e.target.querySelectorAll(".substep-visible"),n=0;n<t.length;n++)t[n].classList.remove("substep-visible")}),!1),e.addEventListener("impress:substep:show",(function(){r(i)}),!1),e.addEventListener("impress:substep:hide",(function(){o(i)}),!1)}(document,window),function(e,t){"use strict";var n=0,i=0,r=0,s=t.innerWidth/20;e.addEventListener("touchstart",(function(e){i=n=e.touches[0].clientX})),e.addEventListener("touchmove",(function(e){var s=e.touches[0].clientX,o=s-n;r=i-s,i=s,t.impress().swipe(o/t.innerWidth)})),e.addEventListener("touchend",(function(){var o=i-n;Math.abs(o)>t.innerWidth/5&&o*r<=0?o>t.innerWidth/5&&r<=0?t.impress().prev():o<-t.innerWidth/5&&r>=0&&t.impress().next():Math.abs(r)>s?r<-s?t.impress().prev():r>s&&t.impress().next():t.impress().goto(e.querySelector("#impress .step.active"))})),e.addEventListener("touchcancel",(function(){t.impress().goto(e.querySelector("#impress .step.active"))}))}(document,window),function(e){"use strict";var t=e.getElementById("impress-toolbar"),n=[],i=function(e){for(var t=e+1;!n[t]&&t<n.length;)t++;if(t<n.length)return t};t&&(t.addEventListener("impress:toolbar:appendChild",(function(r){(function(r){var s="impress-toolbar-group-"+r;if(!n[r]){n[r]=e.createElement("span"),n[r].id=s;var o=i(r);void 0===o?t.appendChild(n[r]):t.insertBefore(n[r],n[o])}return n[r]})(r.detail.group).appendChild(r.detail.element)})),t.addEventListener("impress:toolbar:insertBefore",(function(e){t.insertBefore(e.detail.element,e.detail.before)})),t.addEventListener("impress:toolbar:removeWidget",(function(e){t.removeChild(e.detail.remove)})),e.addEventListener("impress:init",(function(e){e.detail.api.lib.gc.pushCallback((function(){t.innerHTML="",n=[]}))})))}(document);
//# sourceMappingURL=js/impress.min.js.map