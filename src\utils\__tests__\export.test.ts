import { describe, it, expect } from 'vitest'
import { exportToJSON, importFromJSON, exportToHTML } from '../export'
import type { Presentation } from '../../types/presentation'

const mockPresentation: Presentation = {
  id: 'test-ppt',
  name: 'Test Presentation',
  slides: [
    {
      id: 'slide-1',
      title: 'Slide 1',
      elements: [
        {
          id: 'el-1',
          type: 'text',
          frame: { x: 100, y: 100, width: 200, height: 50 },
          data: { html: '<p>Hello World</p>' },
          style: { fontSize: 18, fill: '#000000' }
        }
      ],
      impress: { x: 0, y: 0, scale: 1 }
    }
  ],
  currentSlideId: 'slide-1',
  theme: 'default',
  createdAt: Date.now(),
  updatedAt: Date.now()
}

describe('Export Utils', () => {
  it('should export to JSON', () => {
    const json = exportToJSON(mockPresentation)
    const parsed = JSON.parse(json)
    
    expect(parsed.id).toBe(mockPresentation.id)
    expect(parsed.name).toBe(mockPresentation.name)
    expect(parsed.slides).toHaveLength(1)
  })

  it('should import from JSON', () => {
    const json = exportToJSON(mockPresentation)
    const imported = importFromJSON(json)
    
    expect(imported).not.toBeNull()
    expect(imported?.id).toBe(mockPresentation.id)
    expect(imported?.name).toBe(mockPresentation.name)
    expect(imported?.slides).toHaveLength(1)
  })

  it('should handle invalid JSON import', () => {
    const invalid = '{"invalid": true}'
    const imported = importFromJSON(invalid)
    
    expect(imported).toBeNull()
  })

  it('should export to HTML', async () => {
    const html = await exportToHTML(mockPresentation)

    expect(html).toContain('<!DOCTYPE html>')
    expect(html).toContain('<title>Test Presentation</title>')
    expect(html).toContain('impress')
    expect(html).toContain('data-x="0"')
    expect(html).toContain('Hello World')
  })

  it('should handle empty slides in HTML export', async () => {
    const emptyPresentation: Presentation = {
      ...mockPresentation,
      slides: []
    }

    const html = await exportToHTML(emptyPresentation)

    expect(html).toContain('<!DOCTYPE html>')
    expect(html).toContain('<title>Test Presentation</title>')
  })

  it('should filter hidden slides in HTML export', async () => {
    const presentationWithHidden: Presentation = {
      ...mockPresentation,
      slides: [
        ...mockPresentation.slides,
        {
          id: 'slide-2',
          title: 'Hidden Slide',
          elements: [],
          impress: { x: 1000, y: 0, scale: 1 },
          hidden: true
        }
      ]
    }

    const html = await exportToHTML(presentationWithHidden)

    expect(html).toContain('Hello World')
    expect(html).not.toContain('Hidden Slide')
  })
})
