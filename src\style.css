:root { /* light as default */
  --bg: #ffffff;
  --surface: #fcfcfd;
  --surface-elev: #f7f7f9;
  --fg: #1f2328;
  --muted: #667085;
  --border: #e5e7eb;

  color-scheme: light dark;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: var(--fg);
  background-color: var(--bg);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
:root.dark {
  --bg: #0b0c0f;
  --surface: #0f1115;
  --surface-elev: #171922;
  --fg: #e6e8ea;
  --muted: #9aa4b2;
  --border: #272b35;
}

html, body, #app { height: 100%; }

* { box-sizing: border-box; }

a { color: #3b82f6; text-decoration: none; }
a:hover { color: #2563eb; }

body { margin: 0; }

button { border-radius: 8px; border: 1px solid var(--border); padding: 6px 10px; background: var(--surface-elev); color: var(--fg); }
