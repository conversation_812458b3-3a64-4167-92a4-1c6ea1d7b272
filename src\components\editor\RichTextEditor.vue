<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'

const props = withDefaults(defineProps<{ html: string }>(), { html: '<p></p>' })
const emit = defineEmits<{ 'update:html': [string], 'done': [] }>()

const editor = ref<Editor | null>(null)

onMounted(() => {
  editor.value = new Editor({
    content: props.html,
    extensions: [StarterKit],
    editorProps: {
      attributes: { class: 'rt-editor' },
      handleDOMEvents: {
        keydown: (_view, event) => {
          if (event.key === 'Escape') {
            emit('done')
            return true
          }
          return false
        }
      }
    },
    onUpdate: ({ editor }) => {
      emit('update:html', editor.getHTML())
    }
  })
})

onBeforeUnmount(() => {
  editor.value?.destroy()
  editor.value = null
})

watch(() => props.html, (v) => {
  if (!editor.value) return
  if (v !== editor.value.getHTML()) editor.value.commands.setContent(v)
})
</script>

<template>
  <EditorContent :editor="editor" />
</template>

<style scoped>
.rt-editor { min-height: 100%; outline: none; color: var(--fg); }
:deep(.tiptap) { min-height: 100%; }
</style>
