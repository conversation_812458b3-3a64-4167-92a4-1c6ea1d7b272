/*
    So you like the style of impress.js demo?
    Or maybe you are just curious how it was done?

    You couldn't find a better place to find out!

    Welcome to the stylesheet impress.js demo presentation.

    Please remember that it is not meant to be a part of impress.js and is
    not required by impress.js.
    I expect that anyone creating a presentation for impress.js would create
    their own set of styles.

    But feel free to read through it and learn how to get the most of what
    impress.js provides.

    And let me be your guide.

    Shall we begin?
*/


/*
    We start with a good ol' reset.
    That's the one by <PERSON> http://meyerweb.com/eric/tools/css/reset/

    You can probably argue if it is needed here, or not, but for sure it
    doesn't do any harm and gives us a fresh start.
*/

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
    display: block;
}
body {
    line-height: 1;
}
ol, ul {
    list-style: none;
}
blockquote, q {
    quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

/*
    Now here is when interesting things start to appear.

    We set up <body> styles with default font and nice gradient in the background.
    And yes, there is a lot of repetition there because of -prefixes but we don't
    want to leave anybody behind.
*/
body {
    font-family: 'PT Sans', sans-serif;
    min-height: 740px;

    background: rgb(215, 215, 215);
    background: -webkit-gradient(radial, 50% 50%, 0, 50% 50%, 500, from(rgb(240, 240, 240)), to(rgb(190, 190, 190)));
    background: -webkit-radial-gradient(rgb(240, 240, 240), rgb(190, 190, 190));
    background:    -moz-radial-gradient(rgb(240, 240, 240), rgb(190, 190, 190));
    background:     -ms-radial-gradient(rgb(240, 240, 240), rgb(190, 190, 190));
    background:      -o-radial-gradient(rgb(240, 240, 240), rgb(190, 190, 190));
    background:         radial-gradient(rgb(240, 240, 240), rgb(190, 190, 190));
}

/*
    Now let's bring some text styles back ...
*/
b, strong { font-weight: bold }
i, em { font-style: italic }

/*
    ... and give links a nice look.
*/
a {
    color: inherit;
    text-decoration: none;
    padding: 0 0.1em;
    background: rgba(255,255,255,0.5);
    text-shadow: -1px -1px 2px rgba(100,100,100,0.9);
    border-radius: 0.2em;

    -webkit-transition: 0.5s;
    -moz-transition:    0.5s;
    -ms-transition:     0.5s;
    -o-transition:      0.5s;
    transition:         0.5s;
}

a:hover,
a:focus {
    background: rgba(255,255,255,1);
    text-shadow: -1px -1px 2px rgba(100,100,100,0.5);
}

/*
    Because the main point behind the impress.js demo is to demo impress.js
    we display a fallback message for users with browsers that don't support
    all the features required by it.

    All of the content will be still fully accessible for them, but I want
    them to know that they are missing something - that's what the demo is
    about, isn't it?

    And then we hide the message, when support is detected in the browser.
*/

.fallback-message {
    font-family: sans-serif;
    line-height: 1.3;

    width: 780px;
    padding: 10px 10px 0;
    margin: 20px auto;

    border: 1px solid #E4C652;
    border-radius: 10px;
    background: #EEDC94;
}

.fallback-message p {
    margin-bottom: 10px;
}

.impress-supported .fallback-message {
    display: none;
}

/*
    Now let's style the presentation steps.

    We start with basics to make sure it displays correctly in everywhere ...
*/

.step {
    position: relative;
    width: 900px;
    padding: 40px;
    margin: 20px auto;

    -webkit-box-sizing: border-box;
    -moz-box-sizing:    border-box;
    -ms-box-sizing:     border-box;
    -o-box-sizing:      border-box;
    box-sizing:         border-box;

    font-family: 'PT Serif', georgia, serif;
    font-size: 48px;
    line-height: 1.5;
}

/*
    ... and we enhance the styles for impress.js.

    Basically we remove the margin and make inactive steps a little bit transparent.
*/
.impress-enabled .step {
    margin: 0;
    opacity: 0.3;

    -webkit-transition: opacity 1s;
    -moz-transition:    opacity 1s;
    -ms-transition:     opacity 1s;
    -o-transition:      opacity 1s;
    transition:         opacity 1s;
}

.impress-enabled .step.active { opacity: 1 }

/*
    These 'slide' step styles were heavily inspired by HTML5 Slides:
    http://html5slides.googlecode.com/svn/trunk/styles.css

    ;)

    They cover everything what you see on first three steps of the demo.

    All impress.js steps are wrapped inside a div element of 0 size! This means that relative
    values for width and height (example: `width: 100%`) will not work. You need to use pixel
    values. The pixel values used here correspond to the data-width and data-height given to the
    #impress root element. When the presentation is viewed on a larger or smaller screen, impress.js
    will automatically scale the steps to fit the screen.
*/
.slide {
    display: block;

    width: 900px;
    height: 700px;
    padding: 40px 60px;

    background-color: white;
    border: 1px solid rgba(0, 0, 0, .3);
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .1);

    color: rgb(102, 102, 102);
    text-shadow: 0 2px 2px rgba(0, 0, 0, .1);

    font-family: 'Open Sans', Arial, sans-serif;
    font-size: 30px;
    line-height: 36px;
    letter-spacing: -1px;
}

.slide q {
    display: block;
    font-size: 50px;
    line-height: 72px;

    margin-top: 100px;
}

.slide q strong {
    white-space: nowrap;
}

/*
    And now we start to style each step separately.

    I agree that this may be not the most efficient, object-oriented and
    scalable way of styling, but most of steps have quite a custom look
    and typography tricks here and there, so they had to be styled separately.

    First is the title step with a big <h1> (no room for padding) and some
    3D positioning along Z axis.
*/

#title {
    padding: 0;
}

#title .try {
    font-size: 64px;
    position: absolute;
    top: -0.5em;
    left: 1.5em;

    -webkit-transform: translateZ(20px);
    -moz-transform:    translateZ(20px);
    -ms-transform:     translateZ(20px);
    -o-transform:      translateZ(20px);
    transform:         translateZ(20px);
}

#title h1 {
    font-size: 180px;

    -webkit-transform: translateZ(50px);
    -moz-transform:    translateZ(50px);
    -ms-transform:     translateZ(50px);
    -o-transform:      translateZ(50px);
    transform:         translateZ(50px);
}

#title .footnote {
    font-size: 32px;
}

/*
    Second step is nothing special, just a text with a link, so it doesn't need
    any special styling.

    Let's move to 'big thoughts' with centered text and custom font sizes.
*/
#big {
    width: 600px;
    text-align: center;
    font-size: 60px;
    line-height: 1;
}

#big strong,
#big b {
    display: block;
    font-size: 250px;
    line-height: 250px;
}

#big .thoughts {
    font-size: 90px;
    line-height: 150px;
}

/*
    'Tiny ideas' just need some tiny styling.
*/
#tiny {
    width: 500px;
    text-align: center;
}

/*
    This step has some animated text ...
*/
#ing { width: 500px }

/*
    ... so we define display to `inline-block` to enable transforms and
    transition duration to 0.5s ...
*/
#ing b {
    display: inline-block;
    -webkit-transition: 0.5s;
    -moz-transition:    0.5s;
    -ms-transition:     0.5s;
    -o-transition:      0.5s;
    transition:         0.5s;
}

/*
    ... and we want 'positioning` word to move up a bit when the step gets
    `present` class ...
*/
#ing.present .positioning {
    -webkit-transform: translateY(-10px);
    -moz-transform:    translateY(-10px);
    -ms-transform:     translateY(-10px);
    -o-transform:      translateY(-10px);
    transform:         translateY(-10px);
}

/*
    ... 'rotating' to rotate a quarter of a second later ...
*/
#ing.present .rotating {
    -webkit-transform: rotate(-10deg);
    -moz-transform:    rotate(-10deg);
    -ms-transform:     rotate(-10deg);
    -o-transform:      rotate(-10deg);
    transform:         rotate(-10deg);

    -webkit-transition-delay: 0.25s;
    -moz-transition-delay:    0.25s;
    -ms-transition-delay:     0.25s;
    -o-transition-delay:      0.25s;
    transition-delay:         0.25s;
}

/*
    ... and 'scaling' to scale down after another quarter of a second.
*/
#ing.present .scaling {
    -webkit-transform: scale(0.7);
    -moz-transform:    scale(0.7);
    -ms-transform:     scale(0.7);
    -o-transform:      scale(0.7);
    transform:         scale(0.7);

    -webkit-transition-delay: 0.5s;
    -moz-transition-delay:    0.5s;
    -ms-transition-delay:     0.5s;
    -o-transition-delay:      0.5s;
    transition-delay:         0.5s;
}

/*
    The 'imagination' step is again some boring font-sizing.
*/

#imagination {
    width: 600px;
}

#imagination .imagination {
    font-size: 78px;
}

/*
    There is nothing really special about 'use the source, Luke' step, too,
    except maybe of the Yoda background.

    As you can see below I've 'hard-coded' it in data URL.
    That's not the best way to serve images, but because that's just this one
    I decided it will be OK to have it this way.

    Just make sure you don't blindly copy this approach.
*/
#source {
    width: 700px;
    padding-bottom: 300px;

    /* Yoda Icon :: Pixel Art from Star Wars http://www.pixeljoint.com/pixelart/1423.htm */
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARgAAAEYCAMAAACwUBm+AAAAAXNSR0IArs4c6QAAAKtQTFRFsAAAvbWSLUUrLEQqY1s8UYJMqJ1vNTEgOiIdIzYhjIFVLhsXZ6lgSEIsP2U8JhcCVzMsSXZEgXdOO145XJdWOl03LzAYMk4vSXNExr+hwcuxRTs1Qmk+RW9Am49eFRANQz4pUoNMQWc+OSMDTz0wLBsCNVMxa2NBOyUDUoNNSnlEWo9VRGxAVzYFl6tXCggHbLNmMUIcHhwTXkk5f3VNRT8wUT8xAAAACQocRBWFFwAAAAF0Uk5TAEDm2GYAAAPCSURBVHja7d3JctNAFIZRMwRCCGEmzPM8z/D+T8bu/ptbXXJFdij5fMt2Wuo+2UgqxVmtttq5WVotLzBgwIABAwYMGDCn0qVqbo69psPqVpWx+1XG5iaavF8wYMCAAQMGDBgwi4DJ6Y6qkxB1HNlcN3a92gbR5P2CAQMGDBgwYMCAWSxMlrU+UY5yu2l9okfV4bAxUVbf7TJnAwMGDBgwYMCAAbMLMHeqbGR82Zy+VR1Ht81nVca6R+UdTLaU24Ruzd3qM/e4yjnAgAEDBgwYMGDA7AJMd1l/3NRdVGcj3eX/2WEhCmDGxnM7yqygu8XIPjJj8iN/MGDAgAEDBgwYMAuDGb8q0RGlLCHLv1t9qDKWn3vdNHVuEI6HPaxO9Jo3GDBgwIABAwYMmIXBdC9ShGgMk+XnkXUeuGcsP/e1+lhNnZsL/G5Vs3OAAQMGDBgwYMCAWSxMR3SzOmraG5atdy9wZKzb+vg16qyqe2FltbnAgAEDBgwYMGDALAxmTJSuN3WA76rnVca6GTnemGN1WoEBAwYMGDBgwIBZGMxUomy4+xO899V4LAg5Xnc2MGDAgAEDBgwYMGA218Wq+2K1LDqvY9xZu8zN8fICdM6btYABAwYMGDBgwIABMzfH0+pGU5afze2tXebmeAfVz+p8BQYMGDBgwIABAwbMPBzZ+oWmfJrln1273FhkbHzee9WWbw7AgAEDBgwYMGDALAKm43hcdctKgblcPamOhuXnXlY5Xs6bsW4FGyQCAwYMGDBgwIABswiYMceZKgvMo+h8mrHLTdn676rj+FEFoTtHd8MwOxEYMGDAgAEDBgyYRcBM5UhXqiymW3R3c9ARhWO/OmjqfjVZy+xEYMCAAQMGDBgwYBYG073OnCV0RFNhMhaOa9WfKmOB6XjHMN1tQmaAAQMGDBgwYMCA2VWY7vXjz1U4croAzgPztwIDBgwYMGDAgAEDZhswh035NBw59Dww3RgYMGDAgAEDBgwYMJuD6f4tXT7NUqfCdBvZLkxXdgQGDBgwYMCAAQNmt2DGj8WzwAfV/w7T/aq7mxwwYMCAAQMGDBgwuwqTOo7uTwTngflSzQ3TdaJvAwEDBgwYMGDAgAED5gSvgbyo5oHZ4Pc+gwEDBgwYMGDAgAEzhOm+5G0qTGaAAQMGDBgwYMCAAXNaMOcnls3tNwWm+zRzp54NDBgwYMCAAQMGDJh5YNL36k1TLuGvVq+qnKMbS5n7tulT9asCAwYMGDBgwIABA2ZumKuztLnjgQEDBgwYMGDAgNl5mH/4/ltKA6vBNAAAAABJRU5ErkJggg==);
    background-position: bottom right;
    background-repeat: no-repeat;
}

#source q {
    font-size: 60px;
}

/*
    And the "it's in 3D" step again brings some 3D typography - just for fun.

    Because we want to position <span> elements in 3D we set transform-style to
    `preserve-3d` on the paragraph.
    It is not needed by webkit browsers, but it is in Firefox. It's hard to say
    which behaviour is correct as 3D transforms spec is not very clear about it.
*/
#its-in-3d p {
    -webkit-transform-style: preserve-3d;
    -moz-transform-style:    preserve-3d; /* Y U need this Firefox?! */
    -ms-transform-style:     preserve-3d;
    -o-transform-style:      preserve-3d;
    transform-style:         preserve-3d;
}

/*
    Below we position each word separately along Z axis and we want it to transition
    to default position in 0.5s when the step gets `present` class.

    Quite a simple idea, but lot's of styles and prefixes.
*/
#its-in-3d span,
#its-in-3d b {
    display: inline-block;
    -webkit-transform: translateZ(40px);
    -moz-transform:    translateZ(40px);
    -ms-transform:     translateZ(40px);
    -o-transform:      translateZ(40px);
     transform:        translateZ(40px);

    -webkit-transition: 0.5s;
    -moz-transition:    0.5s;
    -ms-transition:     0.5s;
    -o-transition:      0.5s;
    transition:         0.5s;
}

#its-in-3d .have {
    -webkit-transform: translateZ(-40px);
    -moz-transform:    translateZ(-40px);
    -ms-transform:     translateZ(-40px);
    -o-transform:      translateZ(-40px);
    transform:         translateZ(-40px);
}

#its-in-3d .you {
    -webkit-transform: translateZ(20px);
    -moz-transform:    translateZ(20px);
    -ms-transform:     translateZ(20px);
    -o-transform:      translateZ(20px);
    transform:         translateZ(20px);
}

#its-in-3d .noticed {
    -webkit-transform: translateZ(-40px);
    -moz-transform:    translateZ(-40px);
    -ms-transform:     translateZ(-40px);
    -o-transform:      translateZ(-40px);
    transform:         translateZ(-40px);
}

#its-in-3d .its {
    -webkit-transform: translateZ(60px);
    -moz-transform:    translateZ(60px);
    -ms-transform:     translateZ(60px);
    -o-transform:      translateZ(60px);
    transform:         translateZ(60px);
}

#its-in-3d .in {
    -webkit-transform: translateZ(-10px);
    -moz-transform:    translateZ(-10px);
    -ms-transform:     translateZ(-10px);
    -o-transform:      translateZ(-10px);
    transform:         translateZ(-10px);
}

#its-in-3d .footnote {
    font-size: 32px;

    -webkit-transform: translateZ(-10px);
    -moz-transform:    translateZ(-10px);
    -ms-transform:     translateZ(-10px);
    -o-transform:      translateZ(-10px);
    transform:         translateZ(-10px);
}

#its-in-3d.present span,
#its-in-3d.present b {
    -webkit-transform: translateZ(0px);
    -moz-transform:    translateZ(0px);
    -ms-transform:     translateZ(0px);
    -o-transform:      translateZ(0px);
    transform:         translateZ(0px);
}

/*
    The last step is an overview.
    There is no content in it, so we make sure it's not visible because we want
    to be able to click on other steps.

*/
#overview { display: none }

/*
    We also make other steps visible and give them a pointer cursor using the
    `impress-on-` class.
*/
.impress-on-overview .step {
    opacity: 1;
    cursor: pointer;
}

/*
    Now, when we have all the steps styled let's give users a hint how to navigate
    around the presentation.

    The best way to do this would be to use JavaScript, show a delayed hint for a
    first time users, then hide it and store a status in cookie or localStorage...

    But I wanted to have some CSS fun and avoid additional scripting...

    Let me explain it first, so maybe the transition magic will be more readable
    when you read the code.

    First of all I wanted the hint to appear only when user is idle for a while.
    You can't detect the 'idle' state in CSS, but I delayed a appearing of the
    hint by 5s using transition-delay.

    You also can't detect in CSS if the user is a first-time visitor, so I had to
    make an assumption that I'll only show the hint on the first step. And when
    the step is changed hide the hint, because I can assume that user already
    knows how to navigate.

    To summarize it - hint is shown when the user is on the first step for longer
    than 5 seconds.

    The other problem I had was caused by the fact that I wanted the hint to fade
    in and out. It can be easily achieved by transitioning the opacity property.
    But that also meant that the hint was always on the screen, even if totally
    transparent. It covered part of the screen and you couldn't correctly clicked
    through it.
    Unfortunately you cannot transition between display `block` and `none` in pure
    CSS, so I needed a way to not only fade out the hint but also move it out of
    the screen.

    I solved this problem by positioning the hint below the bottom of the screen
    with CSS transform and moving it up to show it. But I also didn't want this move
    to be visible. I wanted the hint only to fade in and out visually, so I delayed
    the fade in transition, so it starts when the hint is already in its correct
    position on the screen.

    I know, it sounds complicated ... maybe it would be easier with the code?
*/

.hint {
    /*
        We hide the hint until presentation is started and from browsers not supporting
        impress.js, as they will have a linear scrollable view ...
    */
    display: none;

    /*
        ... and give it some fixed position and nice styles.
    */
    position: fixed;
    left: 0;
    right: 0;
    bottom: 200px;

    background: rgba(0,0,0,0.5);
    color: #EEE;
    text-align: center;

    font-size: 50px;
    padding: 20px;

    z-index: 100;

    /*
        By default we don't want the hint to be visible, so we make it transparent ...
    */
    opacity: 0;

    /*
        ... and position it below the bottom of the screen (relative to it's fixed position)
    */
    -webkit-transform: translateY(400px);
    -moz-transform:    translateY(400px);
    -ms-transform:     translateY(400px);
    -o-transform:      translateY(400px);
    transform:         translateY(400px);

    /*
        Now let's imagine that the hint is visible and we want to fade it out and move out
        of the screen.

        So we define the transition on the opacity property with 1s duration and another
        transition on transform property delayed by 1s so it will happen after the fade out
        on opacity finished.

        This way user will not see the hint moving down.
    */
    -webkit-transition: opacity 1s, -webkit-transform 0.5s 1s;
    -moz-transition:    opacity 1s,    -moz-transform 0.5s 1s;
    -ms-transition:     opacity 1s,     -ms-transform 0.5s 1s;
    -o-transition:      opacity 1s,      -o-transform 0.5s 1s;
    transition:         opacity 1s,         transform 0.5s 1s;
}

/*
    Now we 'enable' the hint when presentation is initialized ...
*/
.impress-enabled .hint { display: block }

/*
    ... and we will show it when the first step (with id 'bored') is active.
*/
.impress-on-bored .hint {
    /*
        We remove the transparency and position the hint in its default fixed
        position.
    */
    opacity: 1;

    -webkit-transform: translateY(0px);
    -moz-transform:    translateY(0px);
    -ms-transform:     translateY(0px);
    -o-transform:      translateY(0px);
    transform:         translateY(0px);

    /*
        Now for fade in transition we have the oposite situation from the one
        above.

        First after 4.5s delay we animate the transform property to move the hint
        into its correct position and after that we fade it in with opacity
        transition.
    */
    -webkit-transition: opacity 1s 5s, -webkit-transform 0.5s 4.5s;
    -moz-transition:    opacity 1s 5s,    -moz-transform 0.5s 4.5s;
    -ms-transition:     opacity 1s 5s,     -ms-transform 0.5s 4.5s;
    -o-transition:      opacity 1s 5s,      -o-transform 0.5s 4.5s;
    transition:         opacity 1s 5s,         transform 0.5s 4.5s;
}

/*
    That's all I have for you in this file.
    Thanks for reading. I hope you enjoyed it at least as much as I enjoyed writing it
    for you.
*/
