<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'

const emit = defineEmits<{
  close: []
}>()

const store = usePresentationStore()
const { doc } = storeToRefs(store)
const { t } = useI18n()

const currentSlideIndex = ref(0)
const previewContainer = ref<HTMLElement>()
const impressContainer = ref<HTMLElement>()
const isTransitioning = ref(false)
const isFullscreen = ref(false)

const visibleSlides = computed(() => doc.value.slides.filter(slide => !slide.hidden))
const currentSlide = computed(() => visibleSlides.value[currentSlideIndex.value])
const isFirstSlide = computed(() => currentSlideIndex.value === 0)
const isLastSlide = computed(() => currentSlideIndex.value === visibleSlides.value.length - 1)

// 计算当前幻灯片的 Impress.js 变换
const currentTransform = computed(() => {
  if (!currentSlide.value) return ''

  const impress = currentSlide.value.impress || { x: 0, y: 0, z: 0, scale: 1, rotate: 0, rotateX: 0, rotateY: 0 }

  // 计算变换矩阵，模拟 Impress.js 的效果
  const x = -(impress.x || 0)
  const y = -(impress.y || 0)
  const z = -(impress.z || 0)
  const scale = 1 / (impress.scale || 1)
  const rotate = -(impress.rotate || 0)
  const rotateX = -(impress.rotateX || 0)
  const rotateY = -(impress.rotateY || 0)

  return `translate3d(${x}px, ${y}px, ${z}px) scale(${scale}) rotateZ(${rotate}deg) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`
})

// 获取元素样式的辅助函数
function getElementTextStyle(element: any) {
  const style = element.style || {}
  return {
    fontSize: (style.fontSize || 18) + 'px',
    color: style.fill || '#000',
    textAlign: style.textAlign || 'left',
    fontFamily: style.fontFamily || 'inherit'
  }
}

function getElementShapeStyle(element: any) {
  const style = element.style || {}
  const data = element.data || {}
  return {
    background: style.fill || '#f0f0f0',
    border: `1px solid ${style.stroke || '#ccc'}`,
    borderRadius: data.kind === 'circle' ? '50%' : '4px',
    width: '100%',
    height: '100%'
  }
}

function getElementHtml(element: any) {
  return element.data?.html || ''
}

function getElementImageSrc(element: any) {
  return element.data?.src || ''
}

function hasImageSrc(element: any) {
  return element.type === 'image' && element.data?.src
}

async function nextSlide() {
  if (!isLastSlide.value && !isTransitioning.value) {
    await transitionToSlide(currentSlideIndex.value + 1)
  }
}

async function prevSlide() {
  if (!isFirstSlide.value && !isTransitioning.value) {
    await transitionToSlide(currentSlideIndex.value - 1)
  }
}

async function goToSlide(index: number) {
  if (index >= 0 && index < visibleSlides.value.length && !isTransitioning.value) {
    await transitionToSlide(index)
  }
}

async function transitionToSlide(index: number) {
  if (index === currentSlideIndex.value) return

  isTransitioning.value = true

  // 添加过渡动画
  if (impressContainer.value) {
    impressContainer.value.style.transition = 'transform 1s ease-in-out'
  }

  currentSlideIndex.value = index

  // 等待动画完成
  await new Promise(resolve => setTimeout(resolve, 1000))

  isTransitioning.value = false

  // 移除过渡，避免影响后续操作
  if (impressContainer.value) {
    impressContainer.value.style.transition = ''
  }
}

function closePreview() {
  emit('close')
}

function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

function onKeyDown(e: KeyboardEvent) {
  switch (e.key) {
    case 'ArrowRight':
    case ' ':
      e.preventDefault()
      nextSlide()
      break
    case 'ArrowLeft':
      e.preventDefault()
      prevSlide()
      break
    case 'Home':
      e.preventDefault()
      goToSlide(0)
      break
    case 'End':
      e.preventDefault()
      goToSlide(visibleSlides.value.length - 1)
      break
    case 'Escape':
      e.preventDefault()
      closePreview()
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', onKeyDown)
  
  // 设置当前幻灯片为 store 中选中的幻灯片
  const currentId = store.doc.currentSlideId
  const index = visibleSlides.value.findIndex(slide => slide.id === currentId)
  if (index >= 0) {
    currentSlideIndex.value = index
  }
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeyDown)
})
</script>

<template>
  <div class="preview-window">
    <div class="preview-header">
      <div class="preview-title">
        {{ t('toolbar.preview') }} - {{ doc.name }}
      </div>
      <div class="preview-controls">
        <span class="slide-counter">
          {{ currentSlideIndex + 1 }} / {{ visibleSlides.length }}
        </span>
        <button @click="toggleFullscreen" class="fullscreen-btn" title="Toggle Fullscreen">⛶</button>
        <button @click="closePreview" class="close-btn">✕</button>
      </div>
    </div>

    <div class="preview-content" ref="previewContainer">
      <!-- Impress.js 容器 -->
      <div class="impress-container">
        <div
          class="impress-viewport"
          ref="impressContainer"
          :style="{ transform: currentTransform }"
        >
          <!-- 所有幻灯片 -->
          <div
            v-for="(slide, index) in visibleSlides"
            :key="slide.id"
            class="step"
            :class="{
              active: index === currentSlideIndex,
              past: index < currentSlideIndex,
              future: index > currentSlideIndex
            }"
            :data-x="slide.impress?.x || index * 1200"
            :data-y="slide.impress?.y || 0"
            :data-z="slide.impress?.z || 0"
            :data-scale="slide.impress?.scale || 1"
            :data-rotate="slide.impress?.rotate || 0"
            :data-rotate-x="slide.impress?.rotateX || 0"
            :data-rotate-y="slide.impress?.rotateY || 0"
            :style="{
              transform: `translate3d(${slide.impress?.x || index * 1200}px, ${slide.impress?.y || 0}px, ${slide.impress?.z || 0}px)
                         scale(${slide.impress?.scale || 1})
                         rotateZ(${slide.impress?.rotate || 0}deg)
                         rotateX(${slide.impress?.rotateX || 0}deg)
                         rotateY(${slide.impress?.rotateY || 0}deg)`
            }"
          >
            <h1 v-if="slide.title" class="slide-title">{{ slide.title }}</h1>

            <div class="slide-elements">
              <div
                v-for="element in slide.elements"
                :key="element.id"
                class="slide-element"
                :style="{
                  position: 'absolute',
                  left: element.frame.x + 'px',
                  top: element.frame.y + 'px',
                  width: element.frame.width + 'px',
                  height: element.frame.height + 'px',
                  transform: element.frame.rotate ? `rotate(${element.frame.rotate}deg)` : undefined
                }"
              >
                <!-- 文本元素 -->
                <div
                  v-if="element.type === 'text'"
                  class="text-element"
                  :style="getElementTextStyle(element)"
                  v-html="getElementHtml(element)"
                ></div>

                <!-- 形状元素 -->
                <div
                  v-else-if="element.type === 'shape'"
                  class="shape-element"
                  :style="getElementShapeStyle(element)"
                ></div>

                <!-- 图片元素 -->
                <img
                  v-else-if="hasImageSrc(element)"
                  :src="getElementImageSrc(element)"
                  class="image-element"
                  alt="Slide image"
                  style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="visibleSlides.length === 0" class="no-slides">
        <p>{{ t('preview.noSlides') }}</p>
      </div>
    </div>

    <div class="preview-footer">
      <div class="navigation-controls">
        <button @click="goToSlide(0)" :disabled="isFirstSlide" class="nav-btn">
          ⏮ {{ t('presenter.first') }}
        </button>
        <button @click="prevSlide" :disabled="isFirstSlide" class="nav-btn">
          ⏪ {{ t('presenter.previous') }}
        </button>
        <button @click="nextSlide" :disabled="isLastSlide" class="nav-btn">
          {{ t('presenter.next') }} ⏩
        </button>
        <button @click="goToSlide(visibleSlides.length - 1)" :disabled="isLastSlide" class="nav-btn">
          {{ t('presenter.last') }} ⏭
        </button>
      </div>
      
      <div class="slide-thumbnails">
        <div 
          v-for="(slide, index) in visibleSlides" 
          :key="slide.id"
          class="thumbnail"
          :class="{ active: index === currentSlideIndex }"
          @click="goToSlide(index)"
          :title="slide.title || `Slide ${index + 1}`"
        >
          {{ index + 1 }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.preview-window {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: var(--surface);
  border-bottom: 1px solid var(--border);
}

.preview-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--fg);
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.slide-counter {
  font-size: 14px;
  color: var(--muted);
}

.fullscreen-btn,
.close-btn {
  background: transparent;
  border: 1px solid var(--border);
  color: var(--fg);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.fullscreen-btn:hover {
  background: var(--surface-elev);
  border-color: #3b82f6;
  color: #3b82f6;
}

.close-btn:hover {
  background: var(--surface-elev);
  border-color: #ef4444;
  color: #ef4444;
}

.preview-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f8f9fa;
  perspective: 1000px;
}

.impress-container {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
}

.impress-viewport {
  position: relative;
  width: 100%;
  height: 100%;
  transform-origin: center center;
  transform-style: preserve-3d;
  transition: transform 1s ease-in-out;
}

.step {
  position: absolute;
  width: 1000px;
  height: 700px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  opacity: 0.3;
  transition: opacity 1s;
  transform-origin: center center;
}

.step.active {
  opacity: 1;
}

.step.past {
  opacity: 0.3;
}

.step.future {
  opacity: 0.3;
}

.slide-title {
  margin: 0 0 20px 0;
  font-size: 48px;
  font-weight: 600;
  color: #333;
}

.slide-elements {
  position: relative;
  width: 100%;
  height: calc(100% - 80px);
}

.slide-element {
  position: absolute;
}

.text-element {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.text-element :deep(p) {
  margin: 0;
  line-height: 1.4;
}

.text-element :deep(h1),
.text-element :deep(h2),
.text-element :deep(h3) {
  margin: 0 0 10px 0;
}

.no-slides {
  text-align: center;
  color: var(--muted);
  font-size: 18px;
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--surface);
  border-top: 1px solid var(--border);
}

.navigation-controls {
  display: flex;
  gap: 8px;
}

.nav-btn {
  background: var(--surface-elev);
  color: var(--fg);
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
}

.nav-btn:hover:not(:disabled) {
  background: var(--border);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.slide-thumbnails {
  display: flex;
  gap: 4px;
  max-width: 400px;
  overflow-x: auto;
}

.thumbnail {
  min-width: 32px;
  height: 32px;
  background: var(--surface-elev);
  border: 1px solid var(--border);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  font-weight: 500;
}

.thumbnail:hover {
  background: var(--border);
}

.thumbnail.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 滚动条样式 */
.slide-thumbnails::-webkit-scrollbar {
  height: 4px;
}

.slide-thumbnails::-webkit-scrollbar-track {
  background: var(--surface);
}

.slide-thumbnails::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 2px;
}

.slide-thumbnails::-webkit-scrollbar-thumb:hover {
  background: var(--muted);
}
</style>
