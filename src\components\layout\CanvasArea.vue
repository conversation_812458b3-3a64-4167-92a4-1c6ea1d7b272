<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'
import { Canvas, Rect, Textbox, Line } from 'fabric'
import RichTextEditor from '../editor/RichTextEditor.vue'
import CodeView from './CodeView.vue'


const store = usePresentationStore()
const { currentSlide } = storeToRefs(store)
const { t } = useI18n()

// Tab state
const activeTab = ref<'canvas' | 'code'>('canvas')

const wrapperEl = ref<HTMLDivElement | null>(null)
const canvasEl = ref<HTMLCanvasElement | null>(null)
let fc: Canvas | null = null
let ro: ResizeObserver | null = null
const editing = ref<{ id: string, html: string, x: number, y: number, w: number, h: number } | null>(null)

// align guides
let guides: Line[] = []
function clearGuides() {
  if (!fc || !guides.length) return
  guides.forEach(g => fc!.remove(g))
  guides = []
}
function addVGuide(x: number) {
  if (!fc) return
  const l = new Line([x, 0, x, fc.getHeight()], { stroke: '#3b82f6', selectable: false, evented: false, strokeDashArray: [5, 5] })
  guides.push(l); fc.add(l)
}
function addHGuide(y: number) {
  if (!fc) return
  const l = new Line([0, y, fc.getWidth(), y], { stroke: '#3b82f6', selectable: false, evented: false, strokeDashArray: [5, 5] })
  guides.push(l); fc.add(l)
}

function startInlineEdit(id: string) {
  if (!fc) return
  const slide = currentSlide.value
  const el = slide?.elements.find(e => e.id === id)
  if (!el || el.type !== 'text') return
  editing.value = { id, html: (el.data as any).html || '<p></p>', x: el.frame.x, y: el.frame.y, w: el.frame.width, h: el.frame.height }
  fc.selection = false
}
function finishInlineEdit() { if (fc) fc.selection = true; editing.value = null }

function clearCanvas() {
  if (!fc) return
  fc.getObjects().forEach(o => fc!.remove(o))
  fc.discardActiveObject()
}

function renderSlide() {
  if (!fc) return
  clearCanvas()
  const slide = currentSlide.value
  if (!slide) return
  for (const el of slide.elements) {
    let obj: any
    if (el.type === 'shape' && (el as any).data?.kind === 'rect') {
      obj = new Rect({
        left: el.frame.x, top: el.frame.y,
        width: el.frame.width, height: el.frame.height,
        angle: el.frame.rotate || 0,
        fill: (el.style as any)?.fill ?? '#f59e0b55',
        stroke: (el.style as any)?.stroke ?? '#f59e0b',
        strokeWidth: 1, rx: 4, ry: 4
      })
    } else if (el.type === 'text') {
      const text = (el as any).data?.html?.replace(/<[^>]+>/g, '') || 'Text'
      obj = new Textbox(text, {
        left: el.frame.x, top: el.frame.y,
        width: el.frame.width, height: el.frame.height,
        angle: el.frame.rotate || 0,
        fontSize: Number((el.style as any)?.fontSize) || 18,
        fill: (el.style as any)?.fill ?? '#e5e7eb',
        textAlign: ((el.style as any)?.textAlign as any) || 'left'
      })
      // 双击进入原位富文本编辑（下一步将真正挂载编辑器）
      obj.on('mousedblclick', () => startInlineEdit(el.id))
    } else {
      continue
    }
    ;(obj as any).__elId = el.id
    obj.setControlsVisibility({ mtr: true })
    fc.add(obj)
  }
  fc.requestRenderAll()
}

function bindObjectEvents() {
  if (!fc) return
  fc.on('object:moving', (e: any) => {
    clearGuides()
    const t = e.target as any
    if (!t) return
    const w = t.getScaledWidth ? t.getScaledWidth() : t.width
    const h = t.getScaledHeight ? t.getScaledHeight() : t.height
    let left = t.left || 0
    let top = t.top || 0
    const centerX = (x: number) => x + w / 2
    const centerY = (y: number) => y + h / 2

    const cw = fc!.getWidth(), ch = fc!.getHeight()
    // snap to canvas center
    if (Math.abs(centerX(left) - cw / 2) < 6) { left = cw / 2 - w / 2; addVGuide(cw / 2) }
    if (Math.abs(centerY(top) - ch / 2) < 6) { top = ch / 2 - h / 2; addHGuide(ch / 2) }

    // snap to other objects (edges and centers)
    const others = fc!.getObjects().filter(o => o !== t && (o as any).__elId)
    let snapVX: number | null = null
    let snapHY: number | null = null
    let minDx = 9999, minDy = 9999
    for (const o of others as any[]) {
      const ow = o.getScaledWidth ? o.getScaledWidth() : o.width
      const oh = o.getScaledHeight ? o.getScaledHeight() : o.height
      const ox = o.left || 0, oy = o.top || 0
      const oxs = [ox, ox + ow / 2, ox + ow] // left, center, right
      const tys = [left, centerX(left), left + w]
      for (const xr of oxs) {
        for (let i = 0; i < tys.length; i++) {
          const vx = tys[i]
          const d = Math.abs(vx - xr)
          if (d < 6 && d < minDx) {
            minDx = d
            if (i === 0) left = xr
            else if (i === 1) left = xr - w / 2
            else left = xr - w
            snapVX = xr
          }
        }
      }
      const oys = [oy, oy + oh / 2, oy + oh] // top, center, bottom
      const txs = [top, centerY(top), top + h]
      for (const yr of oys) {
        for (let i = 0; i < txs.length; i++) {
          const vy = txs[i]
          const d = Math.abs(vy - yr)
          if (d < 6 && d < minDy) {
            minDy = d
            if (i === 0) top = yr
            else if (i === 1) top = yr - h / 2
            else top = yr - h
            snapHY = yr
          }
        }
      }
    }
    if (snapVX != null) addVGuide(snapVX)
    if (snapHY != null) addHGuide(snapHY)

    // grid snap (10px)
    if (store.ui.grid) {
      const grid = 10
      if (Math.abs(left - Math.round(left / grid) * grid) < 3) left = Math.round(left / grid) * grid
      if (Math.abs(top - Math.round(top / grid) * grid) < 3) top = Math.round(top / grid) * grid
    }

    t.set({ left, top })
  })
  fc.on('object:modified', (e: any) => {
    clearGuides()
    const target = e.target as any
    if (!target) return
    const id = target.__elId as string | undefined
    if (!id) return
    const frame = {
      x: target.left || 0,
      y: target.top || 0,
      width: target.getScaledWidth ? target.getScaledWidth() : target.width,
      height: target.getScaledHeight ? target.getScaledHeight() : target.height,
      rotate: target.angle || 0,
    }
    if (target.scaleX || target.scaleY) {
      target.set({ scaleX: 1, scaleY: 1, width: frame.width, height: frame.height })
    }
    store.updateElementFrame(id, frame as any)
  })
  fc.on('selection:created', (e: any) => {
    const target = e.selected?.[0] as any
    store.selectedElementId = target?.__elId ?? null
  })
  fc.on('selection:updated', (e: any) => {
    const target = e.selected?.[0] as any
    store.selectedElementId = target?.__elId ?? null
  })
  fc.on('selection:cleared', () => {
    store.selectedElementId = null
  })
}

onMounted(() => {
  if (!canvasEl.value) return
  fc = new Canvas(canvasEl.value, { preserveObjectStacking: true, selection: true })
  bindObjectEvents()
  // 自适应容器尺寸
  const resize = () => {
    if (!wrapperEl.value || !fc) return
    const rect = wrapperEl.value.getBoundingClientRect()
    const w = Math.max(100, rect.width - 24)
    const h = Math.max(100, rect.height - 24)
    fc.setDimensions({ width: w, height: h })
    fc.requestRenderAll()
  }
  ro = new ResizeObserver(resize)
  if (wrapperEl.value) ro.observe(wrapperEl.value)
  resize()
  renderSlide()
})

watch(() => currentSlide.value && currentSlide.value.elements, () => {
  renderSlide()
})

// watch for style changes and update fabric objects directly
watch(() => store.doc.updatedAt, () => {
  if (!fc) return
  const slide = currentSlide.value
  if (!slide) return

  // update existing objects with new styles
  fc.getObjects().forEach((obj: any) => {
    const id = obj.__elId
    if (!id) return
    const el = slide.elements.find(e => e.id === id)
    if (!el) return

    if (el.type === 'text' && obj.type === 'textbox') {
      const style = el.style as any
      if (style) {
        if (style.fontSize !== undefined) obj.set('fontSize', Number(style.fontSize))
        if (style.fill !== undefined) obj.set('fill', String(style.fill))
        if (style.textAlign !== undefined) obj.set('textAlign', String(style.textAlign))
      }
    } else if (el.type === 'shape' && obj.type === 'rect') {
      const style = el.style as any
      if (style) {
        if (style.fill !== undefined) obj.set('fill', String(style.fill))
        if (style.stroke !== undefined) obj.set('stroke', String(style.stroke))
      }
    }
  })
  fc.requestRenderAll()
})

onBeforeUnmount(() => {
  if (ro && wrapperEl.value) ro.unobserve(wrapperEl.value)
  ro = null
  if (fc) { fc.dispose(); fc = null }
})
</script>

<template>
  <main class="canvas-area">
    <div class="canvas-tabs">
      <button
        class="tab-btn"
        :class="{ active: activeTab === 'canvas' }"
        @click="activeTab = 'canvas'"
      >
        {{ t('canvas.canvas') }}
      </button>
      <button
        class="tab-btn"
        :class="{ active: activeTab === 'code' }"
        @click="activeTab = 'code'"
      >
        {{ t('canvas.sourceCode') }}
      </button>
    </div>

    <div class="canvas-content">
      <div v-if="activeTab === 'canvas'" class="canvas-wrapper" ref="wrapperEl">
        <canvas ref="canvasEl"></canvas>
        <div v-if="editing" class="inline-editor" :style="{ left: editing.x + 'px', top: editing.y + 'px', width: editing.w + 'px', height: editing.h + 'px' }">
          <RichTextEditor
            :html="editing.html"
            @update:html="store.updateElementHtml(editing.id, $event)"
            @done="finishInlineEdit()"
          />
        </div>
      </div>

      <CodeView v-if="activeTab === 'code'" />
    </div>
  </main>
</template>

<style scoped>
.canvas-area {
  background: var(--bg);
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.canvas-tabs {
  background: var(--surface);
  border-bottom: 1px solid var(--border);
  padding: 0 16px;
  display: flex;
  gap: 4px;
}

.tab-btn {
  background: transparent;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: var(--muted);
  font-size: 14px;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: var(--fg);
  background: var(--surface-elev);
}

.tab-btn.active {
  color: var(--fg);
  border-bottom-color: #3b82f6;
  font-weight: 500;
}

.canvas-content {
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-wrapper {
  width: calc(100% - 32px);
  height: calc(100% - 32px);
  border: 2px dashed var(--border);
  border-radius: 8px;
  padding: 8px;
  display: grid;
  place-items: center;
  color: var(--muted);
  position: relative;
}

canvas {
  width: 100%;
  height: 100%;
}

.inline-editor {
  position: absolute;
  background: color-mix(in oklab, var(--bg), transparent 10%);
  border: 1px dashed var(--border);
  border-radius: 8px;
  padding: 12px;
  z-index: 100;
}
</style>

