<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from '../../i18n'
import { useTheme } from '../../composables/useTheme'
import { usePresentationStore } from '../../stores/presentation'
import ThemePicker from '../theme/ThemePicker.vue'
import PreviewWindow from '../preview/PreviewWindow.vue'

const props = defineProps<{ title: string, lang?: 'en' | 'zh' }>()
const emit = defineEmits<{ 'update:lang': ['en' | 'zh'] }>()
const { t } = useI18n()
const { setMode, getPreferred } = useTheme()
const store = usePresentationStore()

const theme = ref(getPreferred())
const showThemePicker = ref(false)
const showPreview = ref(false)

const onLangChange = (e: Event) => {
  const v = (e.target as HTMLSelectElement).value as 'en' | 'zh'
  emit('update:lang', v)
}
const onThemeChange = (e: Event) => {
  const v = (e.target as HTMLSelectElement).value as any
  theme.value = v
  setMode(v)
}
const duplicateCurrent = () => { const id = store.doc.currentSlideId; if (id) store.duplicateSlide(id) }
const removeCurrent = () => { const id = store.doc.currentSlideId; if (id) store.removeSlide(id) }
const insertText = () => store.insertElement('text')
const insertRect = () => store.insertElement('shape')

function openPresenterView() {
  const url = new URL(window.location.href)
  url.searchParams.set('presenter', 'true')
  window.open(url.toString(), '_blank', 'width=1200,height=800')
}

function openPreview() {
  showPreview.value = true
}
</script>

<template>
  <header class="toolbar">
    <div class="left">{{ title }}</div>
    <div class="right">
      <button @click="store.addSlide()">+ {{ t('toolbar.newSlide') }}</button>
      <button @click="duplicateCurrent()">{{ t('toolbar.copy') }}</button>
      <button @click="removeCurrent()">{{ t('toolbar.delete') }}</button>
      <button @click="insertText()">{{ t('toolbar.insertText') }}</button>
      <button @click="insertRect()">{{ t('toolbar.insertShape') }}</button>
      <div style="border-left: 1px solid var(--border); height: 24px; margin: 0 8px;"></div>
      <button @click="store.importJSON()">{{ t('app.importJSON') }}</button>
      <button @click="store.exportJSON()">{{ t('app.exportJSON') }}</button>
      <button @click="store.exportHTML()">{{ t('app.exportHTML') }}</button>
      <div style="border-left: 1px solid var(--border); height: 24px; margin: 0 8px;"></div>
      <button @click="openPreview()">{{ t('toolbar.preview') }}</button>
      <button @click="openPresenterView()">{{ t('app.presenterView') }}</button>
      <div style="position: relative;">
        <button @click="showThemePicker = !showThemePicker">{{ t('app.theme') }}</button>
        <div v-if="showThemePicker" style="position: absolute; top: 100%; right: 0; z-index: 1000; margin-top: 4px;">
          <ThemePicker @click.stop />
        </div>
      </div>
      <label style="margin-left:8px;display:flex;align-items:center;gap:6px;">
        {{ t('app.grid') }}
        <input type="checkbox" v-model="store.ui.grid"/>
      </label>
      <label>
        {{ t('app.language') }}
        <select :value="props.lang" @change="onLangChange">
          <option value="zh">中文</option>
          <option value="en">English</option>
        </select>
      </label>
      <label>
        {{ t('app.theme') }}
        <select :value="theme" @change="onThemeChange">
          <option value="system">{{ t('app.system') }}</option>
          <option value="light">{{ t('app.light') }}</option>
          <option value="dark">{{ t('app.dark') }}</option>
        </select>
      </label>
    </div>
  </header>

  <!-- 预览窗口 -->
  <PreviewWindow
    v-if="showPreview"
    @close="showPreview = false"
  />
</template>

<style scoped>
.toolbar { height: 48px; display: flex; align-items: center; justify-content: space-between; padding: 0 12px; border-bottom: 1px solid var(--border); background: var(--surface);
}
.left { font-weight: 600; }
.right { display: flex; gap: 8px; align-items: center; }
.right > button { padding: 4px 8px; }
select { background: var(--surface-elev); color: var(--fg); border: 1px solid var(--border); border-radius: 6px; padding: 4px 8px; }
</style>