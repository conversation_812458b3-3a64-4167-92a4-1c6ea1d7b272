需求设计
1. 项目目标优化
核心目标： 开发一款拥有经典PPT三栏式布局的所见即所得（WYSIWYG）在线编辑器，该编辑器在前端直观操作的背后，能够自动生成和管理Impress.js所需的HTML属性，从而实现PPT般的编辑体验和电影级的演示效果。
用户体验准则：
隐藏技术细节： 用户无需了解data-x, data-rotate等任何Impress.js的特定属性。所有操作都通过图形界面完成。
肌肉记忆的延续： 界面布局、核心快捷键（如Ctrl+C, Ctrl+V, Ctrl+D复制）和基本操作流程应最大程度地复刻主流演示软件（如PowerPoint, Google Slides）的习惯。
所见即所得： 编辑器中的画布（Canvas）必须精确地反映幻灯片的最终外观。
2. 功能需求再设计 (以“PPT用户”为中心)
这是降低切换成本的第一步，用户一打开应用就应该感到熟悉。
左栏 - 幻灯片缩略图/大纲视图：
缩略图模式： 垂直排列所有幻灯片的静态预览图。用户可以通过拖拽直接调整幻灯片顺序。
大纲模式（可选）： 只显示每张幻灯片的标题和主要文本，方便快速浏览和组织逻辑。
操作： 右键点击缩略图应出现菜单，包含“新建幻灯片”、“复制幻灯片”、“删除幻灯片”、“隐藏幻灯片”等选项。
中栏 - 主编辑画布（Canvas）：
这是用户的核心工作区，始终显示当前选中的幻灯片。
边界清晰： 画布应有明确的视觉边界，让用户感觉是在一个“页面”内工作，而不是一个无限大的Impress.js空间。
对象化操作： 插入的所有内容（文本框、图片、形状）都应作为独立的对象。用户可以通过点击选中，然后进行拖拽、缩放、旋转。对象周围应出现控制点和旋转手柄。
右栏 - 属性检查器面板：
此面板是动态的，根据中栏选中的内容显示不同的设置选项。
未选中任何对象时： 显示当前幻灯片的设置。这是Impress.js能力的核心入口。
选中一个文本框时： 显示字体、字号、颜色、对齐、项目符号等文本属性。
选中一张图片时： 显示替换图片、裁剪、滤镜、边框、透明度等图片属性。
这是设计的灵魂，将用户的直观操作“翻译”成Impress.js的代码。
幻灯片管理与Impress.js定位：
新建幻灯片： 当用户点击“新建幻灯片”时，系统自动在后台计算一个合理的data-x, data-y值（例如，在前一张幻灯片的右侧1200像素处），并生成一个新的<div class="step">。用户完全感知不到这个过程。
自由布局模式（高级）： 在右侧的“幻灯片设置”面板中，提供一个“位置与过渡”选项卡。这里提供图形化控件来调整Impress.js的参数：
2D位置： 提供一个微缩的2D坐标系预览图，用户可以拖拽一个小点来直观地设定幻灯片的相对位置，系统自动更新data-x和data-y。
缩放与旋转： 提供滑块（Slider）或输入框，让用户调整“缩放（data-scale）”和“Z轴旋转（data-rotate）”。
3D旋转： 提供一个可以三维拖拽的立方体模型，直观地设置data-rotate-x和data-rotate-y。
过渡动画（impress.js的天生优势）： 系统默认提供平滑的过渡效果。用户可以在“位置与过渡”面板中选择“无动画”、“淡入淡出”等预设，来覆盖默认的3D运镜效果。
内容编辑（所见即所得）：
插入元素： 顶部工具栏提供“插入文本框”、“插入图片”、“插入形状”按钮。点击后，一个默认样式和大小的元素会出现在当前幻灯片的中央，用户可立即开始拖拽和编辑。
绝对定位： 在幻灯片画布内部，所有元素使用CSS的position: absolute以及top, left, width, height进行定位。用户的拖拽和缩放操作，实时修改的是这些CSS属性，这完全符合PPT的内部逻辑。
这是提高效率、降低新用户创作门槛的关键。
幻灯片版式（Layout）：
当用户新建幻灯片时，可以像PPT一样选择版式，如“仅标题”、“标题和内容”、“两栏内容”等。
选择版式后，画布上会自动创建好带有占位符的文本框或图片框。用户只需点击并替换内容即可。
主题（Theme）：
提供一套预设的“设计主题”。每个主题包含了一整套的配色方案、字体组合和背景设计。
用户可以一键切换整个演示文稿的主题，所有幻灯片和元素的样式都会自动更新。
演讲者视图： 必须支持双屏演讲者模式。一个屏幕全屏播放Impress.js的酷炫效果，另一个屏幕（演讲者自己的屏幕）显示：
当前幻灯片
下一张幻灯片的预览
演讲者备注
计时器
导出为单文件HTML： 这是Impress.js的巨大优势。提供一个“导出”按钮，将所有的HTML结构、用户上传的图片（可转为Base64编码）、CSS样式和Impress.js的运行库打包成一个完全自包含的、可离线运行的HTML文件。这比分享一个.pptx文件更加方便，任何有现代浏览器的设备都能直接打开。
3. 技术架构调整建议
前端框架选择： React 或 Vue.js 依然是首选。它们强大的组件化和状态管理能力非常适合构建这样复杂的编辑器界面。例如，可以将左侧缩略图、中间画布、右侧属性面板都作为独立的组件来开发。
画布的实现： 中间的编辑画布是技术难点。需要一个强大的库来处理对象的拖拽、缩放、旋转等交互，并将这些交互的结果实时反映为CSS属性。可以考虑使用如Fabric.js或Konva.js等HTML5 Canvas库，或者基于DOM自行实现一套拖拽控制逻辑。
状态管理核心： 需要一个中心化的“状态存储”（如Redux或Vuex）来管理整个演示文稿的数据结构。这个数据结构是一个JSON对象，它描述了每一页幻灯片的Impress.js属性（data-x等）、页面内的元素（文本、图片等）及其CSS属性。用户的任何操作都是在修改这个JSON对象，而界面则是这个JSON对象的可视化渲染。当“保存”或“演示”时，系统根据这个JSON对象生成最终的Impress.js HTML。