<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'

const props = defineProps<{
  fullscreen?: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

const store = usePresentationStore()
const { doc } = storeToRefs(store)
const { t } = useI18n()

const currentSlideIndex = ref(0)
const isPlaying = ref(false)
const showControls = ref(true)
const controlsTimer = ref<number | null>(null)

const visibleSlides = computed(() => doc.value.slides.filter(slide => !slide.hidden))
const currentSlide = computed(() => visibleSlides.value[currentSlideIndex.value])
const isFirstSlide = computed(() => currentSlideIndex.value === 0)
const isLastSlide = computed(() => currentSlideIndex.value === visibleSlides.value.length - 1)

const slideProgress = computed(() => {
  if (visibleSlides.value.length === 0) return 0
  return ((currentSlideIndex.value + 1) / visibleSlides.value.length) * 100
})

function nextSlide() {
  if (!isLastSlide.value) {
    currentSlideIndex.value++
    resetControlsTimer()
  }
}

function prevSlide() {
  if (!isFirstSlide.value) {
    currentSlideIndex.value--
    resetControlsTimer()
  }
}

function goToSlide(index: number) {
  if (index >= 0 && index < visibleSlides.value.length) {
    currentSlideIndex.value = index
    resetControlsTimer()
  }
}

function togglePlay() {
  isPlaying.value = !isPlaying.value
  resetControlsTimer()
}

function exitPreview() {
  emit('close')
}

function resetControlsTimer() {
  showControls.value = true
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  controlsTimer.value = setTimeout(() => {
    showControls.value = false
  }, 3000)
}

function onMouseMove() {
  resetControlsTimer()
}

function onKeyDown(e: KeyboardEvent) {
  switch (e.key) {
    case 'ArrowRight':
    case ' ':
      e.preventDefault()
      nextSlide()
      break
    case 'ArrowLeft':
      e.preventDefault()
      prevSlide()
      break
    case 'Home':
      e.preventDefault()
      goToSlide(0)
      break
    case 'End':
      e.preventDefault()
      goToSlide(visibleSlides.value.length - 1)
      break
    case 'Escape':
      e.preventDefault()
      exitPreview()
      break
    case 'f':
    case 'F11':
      e.preventDefault()
      toggleFullscreen()
      break
  }
}

function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

onMounted(() => {
  document.addEventListener('keydown', onKeyDown)
  resetControlsTimer()
  
  // 设置当前幻灯片为 store 中选中的幻灯片
  const currentId = store.doc.currentSlideId
  const index = visibleSlides.value.findIndex(slide => slide.id === currentId)
  if (index >= 0) {
    currentSlideIndex.value = index
  }
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeyDown)
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
})
</script>

<template>
  <div 
    class="preview-mode" 
    :class="{ fullscreen: props.fullscreen }"
    @mousemove="onMouseMove"
  >
    <!-- 幻灯片内容 -->
    <div class="slide-container">
      <div v-if="currentSlide" class="slide-content">
        <h1 v-if="currentSlide.title" class="slide-title">{{ currentSlide.title }}</h1>
        
        <div class="slide-elements">
          <div 
            v-for="element in currentSlide.elements" 
            :key="element.id"
            class="slide-element"
            :class="`element-${element.type}`"
            :style="{
              left: element.frame.x + 'px',
              top: element.frame.y + 'px',
              width: element.frame.width + 'px',
              height: element.frame.height + 'px',
              transform: element.frame.rotate ? `rotate(${element.frame.rotate}deg)` : undefined
            }"
          >
            <!-- 文本元素 -->
            <div 
              v-if="element.type === 'text'" 
              class="text-element"
              :style="{
                fontSize: (element.style as any)?.fontSize + 'px' || '18px',
                color: (element.style as any)?.fill || '#000',
                textAlign: (element.style as any)?.textAlign || 'left',
                fontFamily: (element.style as any)?.fontFamily || 'inherit'
              }"
              v-html="element.data.html"
            ></div>
            
            <!-- 形状元素 -->
            <div 
              v-else-if="element.type === 'shape'" 
              class="shape-element"
              :style="{
                background: (element.style as any)?.fill || '#f0f0f0',
                border: `1px solid ${(element.style as any)?.stroke || '#ccc'}`,
                borderRadius: (element.data as any)?.kind === 'circle' ? '50%' : '4px'
              }"
            ></div>
            
            <!-- 图片元素 -->
            <img 
              v-else-if="element.type === 'image' && element.data.src" 
              :src="element.data.src"
              class="image-element"
              alt="Slide image"
            />
          </div>
        </div>
      </div>
      
      <div v-else class="no-slides">
        <p>{{ t('preview.noSlides') }}</p>
      </div>
    </div>

    <!-- 控制栏 -->
    <div class="controls" :class="{ visible: showControls }">
      <div class="controls-left">
        <button @click="exitPreview" class="control-btn exit-btn">
          <span>✕</span>
        </button>
        <span class="slide-counter">
          {{ currentSlideIndex + 1 }} / {{ visibleSlides.length }}
        </span>
      </div>
      
      <div class="controls-center">
        <button @click="goToSlide(0)" :disabled="isFirstSlide" class="control-btn">
          <span>⏮</span>
        </button>
        <button @click="prevSlide" :disabled="isFirstSlide" class="control-btn">
          <span>⏪</span>
        </button>
        <button @click="togglePlay" class="control-btn play-btn">
          <span>{{ isPlaying ? '⏸' : '▶' }}</span>
        </button>
        <button @click="nextSlide" :disabled="isLastSlide" class="control-btn">
          <span>⏩</span>
        </button>
        <button @click="goToSlide(visibleSlides.length - 1)" :disabled="isLastSlide" class="control-btn">
          <span>⏭</span>
        </button>
      </div>
      
      <div class="controls-right">
        <button @click="toggleFullscreen" class="control-btn">
          <span>⛶</span>
        </button>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar" :class="{ visible: showControls }">
      <div class="progress-fill" :style="{ width: slideProgress + '%' }"></div>
    </div>

    <!-- 缩略图导航 -->
    <div class="thumbnail-nav" :class="{ visible: showControls }">
      <div class="thumbnails">
        <div 
          v-for="(slide, index) in visibleSlides" 
          :key="slide.id"
          class="thumbnail"
          :class="{ active: index === currentSlideIndex }"
          @click="goToSlide(index)"
        >
          <div class="thumbnail-content">
            <div class="thumbnail-title">{{ slide.title || `Slide ${index + 1}` }}</div>
            <div class="thumbnail-elements">
              <div 
                v-for="el in slide.elements.slice(0, 3)" 
                :key="el.id"
                class="thumbnail-element"
                :class="`thumb-${el.type}`"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 帮助提示 -->
    <div class="help-hint" :class="{ visible: showControls }">
      <p>{{ t('preview.helpHint') }}</p>
    </div>
  </div>
</template>

<style scoped>
.preview-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  color: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  cursor: none;
}

.preview-mode:not(.fullscreen) {
  cursor: default;
}

.slide-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
}

.slide-content {
  width: 1000px;
  height: 700px;
  background: white;
  color: black;
  border-radius: 8px;
  padding: 40px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.slide-title {
  margin: 0 0 30px 0;
  font-size: 48px;
  font-weight: 600;
  color: #333;
}

.slide-elements {
  position: relative;
  width: 100%;
  height: calc(100% - 80px);
}

.slide-element {
  position: absolute;
}

.text-element {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.text-element :deep(p) {
  margin: 0;
  line-height: 1.4;
}

.text-element :deep(h1),
.text-element :deep(h2),
.text-element :deep(h3) {
  margin: 0 0 10px 0;
}

.shape-element {
  width: 100%;
  height: 100%;
}

.image-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.no-slides {
  text-align: center;
  color: #666;
  font-size: 24px;
}

.controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(0,0,0,0.8);
  padding: 12px 20px;
  border-radius: 30px;
  opacity: 0;
  transition: opacity 0.3s;
}

.controls.visible {
  opacity: 1;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.controls-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn {
  background: transparent;
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255,255,255,0.2);
  border-color: rgba(255,255,255,0.5);
}

.control-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.exit-btn {
  background: rgba(255,0,0,0.2);
  border-color: rgba(255,0,0,0.5);
}

.exit-btn:hover {
  background: rgba(255,0,0,0.4);
}

.play-btn {
  width: 50px;
  height: 50px;
  font-size: 20px;
  background: rgba(255,255,255,0.1);
}

.slide-counter {
  font-size: 14px;
  color: rgba(255,255,255,0.8);
  min-width: 60px;
  text-align: center;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255,255,255,0.2);
  opacity: 0;
  transition: opacity 0.3s;
}

.progress-bar.visible {
  opacity: 1;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s;
}

.thumbnail-nav {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  max-height: 400px;
  overflow-y: auto;
  opacity: 0;
  transition: opacity 0.3s;
}

.thumbnail-nav.visible {
  opacity: 1;
}

.thumbnails {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.thumbnail {
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.thumbnail:hover {
  background: rgba(255,255,255,0.2);
}

.thumbnail.active {
  background: rgba(59,130,246,0.3);
  border-color: #3b82f6;
}

.thumbnail-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.thumbnail-title {
  font-size: 12px;
  font-weight: 500;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.thumbnail-elements {
  display: flex;
  gap: 2px;
}

.thumbnail-element {
  width: 12px;
  height: 8px;
  border-radius: 2px;
}

.thumb-text {
  background: rgba(255,255,255,0.6);
}

.thumb-shape {
  background: rgba(59,130,246,0.6);
}

.thumb-image {
  background: rgba(34,197,94,0.6);
}

.help-hint {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0,0,0,0.8);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  color: rgba(255,255,255,0.8);
  opacity: 0;
  transition: opacity 0.3s;
}

.help-hint.visible {
  opacity: 1;
}

/* 滚动条样式 */
.thumbnail-nav::-webkit-scrollbar {
  width: 4px;
}

.thumbnail-nav::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 2px;
}

.thumbnail-nav::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.3);
  border-radius: 2px;
}

.thumbnail-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.5);
}
</style>
