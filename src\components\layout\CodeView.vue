<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { usePresentationStore } from '../../stores/presentation'
import { useI18n } from '../../i18n'
import { exportToHTML } from '../../utils/export'

const store = usePresentationStore()
const { doc } = storeToRefs(store)
const { t } = useI18n()

const codeTab = ref<'html' | 'slide' | 'full'>('html')

const htmlCode = ref<string>('Loading...')

// 异步加载 HTML 代码
async function loadHTMLCode() {
  try {
    htmlCode.value = await exportToHTML(doc.value)
  } catch (error) {
    htmlCode.value = 'Error loading HTML export'
    console.error('Failed to export HTML:', error)
  }
}

// 监听文档变化，重新生成 HTML
watch(doc, loadHTMLCode, { deep: true })

// 初始加载
loadHTMLCode()

const slideJsonCode = computed(() => {
  return JSON.stringify(doc.value, null, 2)
})

const currentSlideCode = computed(() => {
  const slide = store.currentSlide
  if (!slide) return '{}'
  return JSON.stringify(slide, null, 2)
})

const currentCode = computed(() => {
  switch (codeTab.value) {
    case 'html': return htmlCode.value
    case 'slide': return currentSlideCode.value
    case 'full': return slideJsonCode.value
    default: return ''
  }
})

const currentTitle = computed(() => {
  switch (codeTab.value) {
    case 'html': return t('codeView.htmlExport')
    case 'slide': return t('codeView.currentSlide')
    case 'full': return t('codeView.fullDocument')
    default: return ''
  }
})

const currentDescription = computed(() => {
  switch (codeTab.value) {
    case 'html': return t('codeView.htmlExportDesc')
    case 'slide': return t('codeView.currentSlideDesc')
    case 'full': return t('codeView.fullDocumentDesc')
    default: return ''
  }
})

function copyToClipboard() {
  navigator.clipboard.writeText(currentCode.value).then(() => {
    // Could add a toast notification here
    console.log('Code copied to clipboard')
  }).catch(err => {
    console.error('Failed to copy code:', err)
  })
}
</script>

<template>
  <div class="code-view">
    <div class="code-tabs">
      <div class="tab-buttons">
        <button
          class="tab-btn"
          :class="{ active: codeTab === 'html' }"
          @click="codeTab = 'html'"
        >
          {{ t('codeView.htmlExport') }}
        </button>
        <button
          class="tab-btn"
          :class="{ active: codeTab === 'slide' }"
          @click="codeTab = 'slide'"
        >
          {{ t('codeView.currentSlide') }}
        </button>
        <button
          class="tab-btn"
          :class="{ active: codeTab === 'full' }"
          @click="codeTab = 'full'"
        >
          {{ t('codeView.fullDocument') }}
        </button>
      </div>
      <button class="copy-btn" @click="copyToClipboard()">{{ t('codeView.copy') }}</button>
    </div>

    <div class="code-content">
      <div class="section-header">
        <h3>{{ currentTitle }}</h3>
        <p>{{ currentDescription }}</p>
      </div>
      <pre class="code-block"><code>{{ currentCode }}</code></pre>
    </div>
  </div>
</template>

<style scoped>
.code-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg);
}

.code-tabs {
  background: var(--surface);
  border-bottom: 1px solid var(--border);
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-buttons {
  display: flex;
  gap: 4px;
}

.copy-btn {
  background: var(--surface-elev);
  color: var(--fg);
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.copy-btn:hover {
  background: var(--border);
}

.tab-btn {
  background: transparent;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: var(--muted);
  font-size: 14px;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: var(--fg);
  background: var(--surface-elev);
}

.tab-btn.active {
  color: var(--fg);
  border-bottom-color: #3b82f6;
  font-weight: 500;
}

.code-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.section-header {
  margin-bottom: 12px;
}

.section-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--fg);
}

.section-header p {
  margin: 0;
  font-size: 14px;
  color: var(--muted);
}

.code-block {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 16px;
  font-family: 'Courier New', Consolas, Monaco, monospace;
  font-size: 12px;
  line-height: 1.5;
  color: var(--fg);
  overflow: auto;
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

.code-block code {
  color: inherit;
  background: none;
  padding: 0;
  font-size: inherit;
}

/* Syntax highlighting for JSON */
.code-block {
  color: var(--fg);
}

/* Simple JSON syntax highlighting */
.code-block :deep(.string) { color: #22c55e; }
.code-block :deep(.number) { color: #3b82f6; }
.code-block :deep(.boolean) { color: #f59e0b; }
.code-block :deep(.null) { color: #ef4444; }
.code-block :deep(.key) { color: #8b5cf6; }
</style>
